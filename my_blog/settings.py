"""
Django settings for my_blog project.

Generated by 'django-admin startproject' using Django 2.1.

For more information on this file, see
https://docs.djangoproject.com/en/2.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.1/ref/settings/
"""

import os

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/2.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '__=s9@oixaun$x^g7-4#10wf_*7zvb8)kl1$j82fj&cyq%^o^3'

# SECURITY WARNING: don't run with debug turned on in production!

# 部署到线上时为 False; 读者在本地调试时请修改为 True
# DEBUG = False
DEBUG = True

# ALLOWED_HOSTS = ['*************']
ALLOWED_HOSTS = ['*']
# ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    # default apps
    'lab_poll',
    'ckeditor',
    'km_kat',
    'mutation',
    'crispy_forms',
    'oss_upload',
    'modelsee',
    'inner_doc',
    'allauth',
    'toolsmap',
    'django.contrib.contenttypes',
    'userprofile',
    'notice',
    'paper_review',
    # 可添加需要的第三方登录
    'allauth.socialaccount',  # 如果您需要社交登录功能
    'django.contrib.sessions',
    'django.contrib.staticfiles',
    'allauth.socialaccount.providers.google',  # 示例：Google 社交登录
    'deltaG',
    'file_upload',
    'corsheaders',
    'mptt',
    'password_reset',
    # Third party apps
    'bootstrap4',
    'practice',
    'django.contrib.auth',
    'article',
    'SolP',
    'notifications',
    'django.contrib.messages',
    'allauth.socialaccount.providers.weibo',
    'task_list',
    'comment',
    'multiselectfield',
    'cpd_cas',
    'django.contrib.sites',
    'taggit',
    'sci_hub',
    'allauth.account',
    'allauth.socialaccount.providers.github',
    'django.contrib.admin',
    'mtc_tools',
    'enzyme_database',
    'enzyme_register',
    'Info',
    'USalign',
    'docking_md',
    'flux_analysis',
    'Microbiome',
    'ShortestPath',
    'threeBuilding',
    'rag',
    'mRNA',
    'rest_framework',
    'pipeline',
    'mtc_agent'
    # 'sphinxdoc',
    # 'haystack',
]

# HAYSTACK_CONNECTIONS = {
#     'default': {
#         'ENGINE': 'haystack.backends.whoosh_backend.WhooshEngine',
#         'PATH': os.path.join(BASE_DIR, 'whoosh_index'),
#     },
# }


REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication'
    ],
}


MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'userprofile.AdminLoginMiddleware.SingleSessionPerUserMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'allauth.account.middleware.AccountMiddleware',
    'userprofile.AdminLoginMiddleware.AdminLoginMiddleware',
]

CSRF_TRUSTED_ORIGINS = ['https://*.mtc-lab.cn']
CORS_ALLOW_ALL_ORIGINS = True

CORS_ALLOWED_ORIGINS = [
    "https://www.mtc-lab.cn",
    "http://localhost:5000",
    "http://localhost:1234",  # 添加这一行
    "http://127.0.0.1:1234",
    
    # "http://***********:5000",
    # 添加其他允许的域名，如果有多个域名需要允许
    
    # 添加其他允许的域名，如果有多个域名需要允许
]
ROOT_URLCONF = 'my_blog.urls'
# SECURE_SSL_REDIRECT = True # 确保设定为https
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        # 定义模板位置
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
            'libraries': {
                'my_customer_tags': 'file_upload.templatetags.pagetag',
                'my_filters': 'ShortestPath.templatetags.my_filter',
                'smiles_tags': 'flux_analysis.templatetags.smiles_tags',
            },

        },
    },
]

WSGI_APPLICATION = 'my_blog.wsgi.application'

# Database
# https://docs.djangoproject.com/en/2.1/ref/settings/#databases


# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'mtc_lab',
#         'USER': 'mtc_lab',
#         'PASSWORD': 'xiamenglei321@TJ',  # 请换成自己的密码
#         'HOST': 'rm-bp183e82141248bm3vo.mysql.rds.aliyuncs.com',  # 如果不能连接，改成localhost试下
#         'POST': '3306',
#     }
# }


DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
    }
}
# ... existing code ...

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
#     }
# }

# ... existing code ...





# Password validation
# https://docs.djangoproject.com/en/2.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/2.1/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.1/howto/static-files/

# 静态文件地址
STATIC_URL = '/static/'

STATICFILES_DIRS = (
    os.path.join(BASE_DIR, "static"),
)

# 静态文件收集目录
STATIC_ROOT = os.path.join(BASE_DIR, 'collected_static')

# 媒体文件地址
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media/')

CKEDITOR_CONFIGS = {
    # django-ckeditor默认使用default配置
    'default': {
        # 编辑器宽度自适应
        'width': 'auto',
        'height': '250px',
        # tab键转换空格数
        'tabSpaces': 4,
        # 工具栏风格
        'toolbar': 'Custom',
        # 工具栏按钮
        'toolbar_Custom': [
            # 表情 代码块
            ['Smiley', 'CodeSnippet'],
            # 字体风格
            ['Bold', 'Italic', 'Underline', 'RemoveFormat', 'Blockquote'],
            # 字体颜色
            ['TextColor', 'BGColor'],
            # 链接
            ['Link', 'Unlink'],
            # 列表
            ['NumberedList', 'BulletedList'],
            # 最大化
            ['Maximize']
        ],
        # 插件
        'extraPlugins': ','.join(['codesnippet', 'prism', 'widget', 'lineutils']),
    }
}

AUTHENTICATION_BACKENDS = (
    # 此项使 Django 后台可独立于 allauth 登录
    'django.contrib.auth.backends.ModelBackend',
    # 配置 allauth 独有的认证方法，如 email 登录
    'allauth.account.auth_backends.AuthenticationBackend',
)

# 设置站点
SITE_ID = 1
# 重定向 url
LOGIN_REDIRECT_URL = '/'

# LOGGING = {
#     'version': 1,
#     'handlers': {
#         'file': {
#             'level': 'INFO',
#             'class': 'logging.FileHandler',
#             'filename': os.path.join(BASE_DIR, 'logs/debug.log'),
#         },
#     },
#     'loggers': {
#         'django': {
#             'handlers': ['file'],
#             'level': 'INFO',
#         },
#     },
# }

# LOGGING = {
#     'version': 1,
#     'disable_existing_loggers': False,
#     'formatters': {
#         'verbose': {
#             'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
#             'style': '{',
#         },
#         'simple': {
#             'format': '{levelname} {message}',
#             'style': '{',
#         },
#     },
#     'filters': {
#         'require_debug_true': {
#             '()': 'django.utils.log.RequireDebugTrue',
#         },
#     },
#     'handlers': {
#         'console': {
#             'level': 'INFO',
#             'filters': ['require_debug_true'],
#             'class': 'logging.StreamHandler',
#             'formatter': 'simple'
#         },
#         'mail_admins': {
#             'level': 'ERROR',
#             'class': 'django.utils.log.AdminEmailHandler',
#             'formatter': 'verbose',
#         },
#         'file': {
#             'level': 'WARNING',
#             # 'class': 'logging.FileHandler',
#             'class': 'logging.handlers.TimedRotatingFileHandler',
#             'when': 'midnight',
#             'backupCount': 30,
#             'filename': os.path.join(BASE_DIR, 'logs/debug.log'),
#             'formatter': 'verbose',
#         },
#     },
#     'loggers': {
#         'django': {
#             'handlers': ['console'],
#             'propagate': True,
#         },
#         'django.request': {
#             'handlers': ['file', 'mail_admins'],
#             'level': 'WARNING',
#             'propagate': False,
#         },
#     }
# }
# 夏梦雷后期添加

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_USE_TLS = True
EMAIL_USE_SSL = False
EMAIL_HOST = 'smtp.163.com'
EMAIL_PORT = 25
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'FOAYAIHGFYVKZCTD'
DEFAULT_FROM_EMAIL = EMAIL_HOST_USER

CRISPY_TEMPLATE_PACK = 'bootstrap4'

AUTHENTICATION_BACKENDS = (
    # ...
    'allauth.account.auth_backends.AuthenticationBackend',
)

AUTHENTICATION_CLASSES = (
    # ...
    'allauth.account.auth_backends.AuthenticationBackend',
)

# SITE_ID = 1
# SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
ACCOUNT_DEFAULT_HTTP_PROTOCOL = "https"

DOCS_URL = '/docs/'
DOCS_ROOT = os.path.join(BASE_DIR, 'docs/html')

# 添加：2024-03-21
DATA_UPLOAD_MAX_NUMBER_FILES = 3000

# SPHINXDOC_ROOT_1 = os.path.join(BASE_DIR, 'docs','Web_2')
# SPHINXDOC_ROOT_2 = os.path.join(BASE_DIR, 'docs','Web_2')

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'pssm.log'),
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'mutation': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'
