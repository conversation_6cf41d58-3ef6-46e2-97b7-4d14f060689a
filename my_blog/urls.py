﻿from django.contrib import admin
# 记得引入include
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static

import notifications.urls
from django.views.static import serve

import km_kat.views
from article.views import article_list, first_Page, tiaoshi

# 存放了映射关系的列表
urlpatterns = [
    path('admin/', admin.site.urls),
    # home
    path('', first_Page, name='first-page'),
    # path('', tiaoshi, name='tiaoshi'),
    # 为了防止错误，该链接依旧不变。在以后的开发中，应高度重视。
    path('article_list', article_list, name='home'),
    # 重置密码app
    path('password-reset/', include('password_reset.urls')),
    # 新增代码，配置app的url
    path('article/', include('article.urls', namespace='article')),
    # 用户管理
    path('userprofile/', include('userprofile.urls', namespace='userprofile')),
    # 评论
    path('comment/', include('comment.urls', namespace='comment')),
    # djang-notifications
    path('inbox/notifications/', include(notifications.urls, namespace='notifications')),
    # notice
    path('notice/', include('notice.urls', namespace='notice')),
    # django-allauth
    # path('accounts/', include('allauth.urls')),
    path('file_upload/', include('file_upload.urls', namespace='file_upload')),
    path('sci_hub/', include('sci_hub.urls', namespace='sci_hub')),
    path('oss_upload/', include('oss_upload.urls', namespace='oss_upload')),
    path('lab_poll/', include('lab_poll.urls', namespace='lab_poll')),
    # path('task_list/', include('task_list.urls', namespace='task_list')),
    path('cpd_cas/', include('cpd_cas.urls', namespace='cpd_cas')),
    path('modelsee/', include('modelsee.urls', namespace='modelsee')),
    re_path(r'^media/(?P<path>.*)$', serve, {'document_root': settings.MEDIA_ROOT}, name='media'),
    path('paper_review/', include('paper_review.urls', namespace='paper_view')),
    path('practice/', include('practice.urls', namespace='practice')),
    path('deltaG/', include('deltaG.urls', namespace='deltaG')),
    path('inner_doc/', include('inner_doc.urls', namespace='inner_doc')),
    path('mtc-tools/', include('mtc_tools.urls', namespace='mtc-tools')),

    ## 新的路径
    path('km/', include("km_kat.urls"), name='km'),
    path('mutation/', include("mutation.urls"), name='mutation'),
    path('userprofile/', include('userprofile.urls', namespace='userprofile')),
    # path('accounts/', include('allauth.urls')),
    # 重置密码app
    # 新增代码，配置app的url
    path('article/', include('article.urls', namespace='article')),
    # 评论
    path('comment/', include('comment.urls', namespace='comment')),
    # djang-notifications
    path('inbox/notifications/', include(notifications.urls, namespace='notifications')),
    # notice
    path('notice/', include('notice.urls', namespace='notice')),
    path('SolP/', include('SolP.urls', namespace='Solp')),
    path('toolsmap/', include('toolsmap.urls', namespace='toolsmap')),
    path('enzyme_database/', include('enzyme_database.urls', namespace='enzyme_database')),
    path('enzyme_register/', include('enzyme_register.urls', namespace='enzyme_register')),
    path('Info/', include('Info.urls', namespace='Info')),
    path('USalign/', include('USalign.urls', namespace='USalign')),
    path('docking_md/', include('docking_md.urls', namespace='docking_md')),
    path('flux_analysis/', include('flux_analysis.urls', namespace='flux_analysis')),
    path('Microbiome/', include('Microbiome.urls', namespace='Microbiome')),
    path('shortestpath/', include('ShortestPath.urls', namespace='shortestpath')),
    # path(r'docs/', include('sphinxdoc.urls')),
    # path('search/', include('haystack.urls')),
    path('threeBuilding/', include("threeBuilding.urls"), name='threeBuilding'),
    path('rag/', include('rag.urls', namespace='rag')),
    path('mRNA/', include('mRNA.urls', namespace='mRNA')),
    path('tustkcat/', km_kat.views.tustkcat, name='tustkcat'),
    path('pipeline/', include('pipeline.urls')),
    path('mtc_agent/', include('mtc_agent.urls', namespace='mtc_agent')),

]
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += static(settings.DOCS_URL, document_root=settings.DOCS_ROOT)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
