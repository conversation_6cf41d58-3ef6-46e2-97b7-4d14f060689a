{% extends 'base.html' %}
{% load static %}

{% block title %}MTC Agent - 智能对话{% endblock %}

{% block extra_css %}
<style>
    body {
        background-color: #f5f7fa;
    }
    
    .chat-container {
        height: calc(100vh - 100px);
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .chat-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        text-align: center;
    }
    
    .chat-header h3 {
        margin: 0;
        font-weight: 600;
    }
    
    .chat-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-size: 0.9rem;
    }
    
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        background: #f8f9fa;
    }
    
    .message {
        margin-bottom: 1rem;
        display: flex;
        align-items: flex-start;
    }
    
    .message.user {
        justify-content: flex-end;
    }
    
    .message.assistant {
        justify-content: flex-start;
    }
    
    .message-content {
        max-width: 70%;
        padding: 1rem 1.5rem;
        border-radius: 20px;
        position: relative;
        word-wrap: break-word;
    }
    
    .message.user .message-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-bottom-right-radius: 5px;
    }
    
    .message.assistant .message-content {
        background: white;
        color: #333;
        border: 1px solid #e9ecef;
        border-bottom-left-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    
    .message-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        margin: 0 0.5rem;
    }
    
    .message.user .message-avatar {
        background: #667eea;
        color: white;
        order: 2;
    }
    
    .message.assistant .message-avatar {
        background: #28a745;
        color: white;
    }
    
    .message-time {
        font-size: 0.75rem;
        opacity: 0.7;
        margin-top: 0.5rem;
    }
    
    .chat-input-area {
        padding: 1.5rem;
        background: white;
        border-top: 1px solid #e9ecef;
    }
    
    .file-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .file-upload-area:hover,
    .file-upload-area.dragover {
        border-color: #667eea;
        background-color: #f8f9ff;
    }
    
    .file-upload-area.has-files {
        border-color: #28a745;
        background-color: #f8fff9;
    }
    
    .uploaded-files {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .file-tag {
        background: #e9ecef;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .file-tag.json {
        background: #d4edda;
        color: #155724;
    }
    
    .file-tag.txt {
        background: #d1ecf1;
        color: #0c5460;
    }
    
    .file-tag .remove-file {
        cursor: pointer;
        color: #dc3545;
        font-weight: bold;
    }
    
    .input-group {
        position: relative;
    }
    
    .form-control {
        border-radius: 25px;
        border: 1px solid #dee2e6;
        padding: 0.75rem 1.5rem;
        padding-right: 4rem;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .send-button {
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    
    .send-button:hover {
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .send-button:disabled {
        background: #6c757d;
        cursor: not-allowed;
        transform: translateY(-50%);
    }
    
    .typing-indicator {
        display: none;
        padding: 1rem 1.5rem;
        background: white;
        border-radius: 20px;
        border-bottom-left-radius: 5px;
        max-width: 70%;
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    
    .typing-dots {
        display: flex;
        gap: 0.25rem;
    }
    
    .typing-dot {
        width: 8px;
        height: 8px;
        background: #667eea;
        border-radius: 50%;
        animation: typing 1.4s infinite ease-in-out;
    }
    
    .typing-dot:nth-child(1) { animation-delay: -0.32s; }
    .typing-dot:nth-child(2) { animation-delay: -0.16s; }
    
    @keyframes typing {
        0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
    }
    
    .function-result {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1rem;
        margin-top: 0.5rem;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
    }
    
    .function-result.success {
        border-color: #28a745;
        background: #f8fff9;
    }
    
    .function-result.error {
        border-color: #dc3545;
        background: #fff5f5;
    }
    
    .welcome-message {
        text-align: center;
        padding: 2rem;
        color: #6c757d;
    }
    
    .welcome-message h4 {
        color: #667eea;
        margin-bottom: 1rem;
    }
    
    .sample-questions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;
        margin-top: 1rem;
    }
    
    .sample-question {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 15px;
        padding: 0.5rem 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }
    
    .sample-question:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let sessionId = '{{ session.id }}';
    let uploadedFiles = [];

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        setupFileUpload();
        scrollToBottom();
        document.getElementById('messageInput').focus();
    });

    // 设置文件上传
    function setupFileUpload() {
        const fileUploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('fileInput');

        // 点击上传
        fileUploadArea.addEventListener('click', function(e) {
            if (e.target === fileUploadArea || e.target.closest('.file-upload-area')) {
                fileInput.click();
            }
        });

        // 文件选择
        fileInput.addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });

        // 拖拽上传
        fileUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        });

        fileUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
        });

        fileUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });
    }

    // 处理文件
    function handleFiles(files) {
        for (let file of files) {
            if (file.type === 'application/json' || file.name.endsWith('.json')) {
                addFile(file, 'json');
            } else if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
                addFile(file, 'txt');
            } else {
                alert('只支持 JSON 和 TXT 文件格式');
            }
        }
        updateFileUploadArea();
    }

    // 添加文件
    function addFile(file, type) {
        const fileObj = {
            file: file,
            name: file.name,
            type: type,
            size: file.size
        };

        uploadedFiles.push(fileObj);
        displayUploadedFiles();
    }

    // 显示已上传文件
    function displayUploadedFiles() {
        const container = document.getElementById('uploadedFiles');
        container.innerHTML = '';

        uploadedFiles.forEach((fileObj, index) => {
            const fileTag = document.createElement('div');
            fileTag.className = `file-tag ${fileObj.type}`;
            fileTag.innerHTML = `
                <i class="fas fa-file-${fileObj.type === 'json' ? 'code' : 'alt'}"></i>
                ${fileObj.name}
                <span class="remove-file" onclick="removeFile(${index})">&times;</span>
            `;
            container.appendChild(fileTag);
        });
    }

    // 移除文件
    function removeFile(index) {
        uploadedFiles.splice(index, 1);
        displayUploadedFiles();
        updateFileUploadArea();
    }

    // 更新文件上传区域状态
    function updateFileUploadArea() {
        const fileUploadArea = document.getElementById('fileUploadArea');
        if (uploadedFiles.length > 0) {
            fileUploadArea.classList.add('has-files');
        } else {
            fileUploadArea.classList.remove('has-files');
        }
    }

    // 处理键盘事件
    function handleKeyPress(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendMessage();
        }
    }

    // 发送示例问题
    function sendSampleQuestion(question) {
        document.getElementById('messageInput').value = question;
        sendMessage();
    }

    // 发送消息
    async function sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const message = messageInput.value.trim();

        if (!message && uploadedFiles.length === 0) {
            return;
        }

        // 禁用输入
        messageInput.disabled = true;
        sendButton.disabled = true;

        // 显示用户消息
        if (message) {
            addMessage('user', message);
        }

        // 显示文件信息
        if (uploadedFiles.length > 0) {
            const fileInfo = uploadedFiles.map(f => `📎 ${f.name}`).join('\n');
            addMessage('user', `上传文件:\n${fileInfo}`);
        }

        // 清空输入
        messageInput.value = '';

        // 显示打字指示器
        showTypingIndicator();

        try {
            // 准备文件数据
            const filesData = await Promise.all(
                uploadedFiles.map(async (fileObj) => {
                    const content = await readFileContent(fileObj.file);
                    return {
                        name: fileObj.name,
                        type: fileObj.type,
                        content: content
                    };
                })
            );

            // 发送请求
            const response = await fetch('{% url "mtc_agent:agent_chat_api" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    message: message,
                    session_id: sessionId,
                    files: filesData
                })
            });

            const data = await response.json();

            if (data.success) {
                sessionId = data.session_id;
                addMessage('assistant', data.message);

                // 显示函数执行结果
                if (data.function_result) {
                    addFunctionResult(data.function_result);
                }
            } else {
                addMessage('assistant', `抱歉，出现了错误：${data.error}`);
            }

        } catch (error) {
            console.error('发送消息失败:', error);
            addMessage('assistant', '抱歉，网络连接出现问题，请稍后重试。');
        } finally {
            // 隐藏打字指示器
            hideTypingIndicator();

            // 重新启用输入
            messageInput.disabled = false;
            sendButton.disabled = false;
            messageInput.focus();

            // 清空上传的文件
            uploadedFiles = [];
            displayUploadedFiles();
            updateFileUploadArea();
        }
    }

    // 读取文件内容
    function readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    }

    // 添加消息到聊天界面
    function addMessage(role, content) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;

        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        if (role === 'assistant') {
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    ${content.replace(/\n/g, '<br>')}
                    <div class="message-time">${timeString}</div>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${content.replace(/\n/g, '<br>')}
                    <div class="message-time">${timeString}</div>
                </div>
                <div class="message-avatar">
                    <i class="fas fa-user"></i>
                </div>
            `;
        }

        messagesContainer.insertBefore(messageDiv, document.getElementById('typingIndicator'));
        scrollToBottom();
    }

    // 添加函数执行结果
    function addFunctionResult(result) {
        const messagesContainer = document.getElementById('chatMessages');
        const lastMessage = messagesContainer.querySelector('.message.assistant:last-of-type .message-content');

        if (lastMessage) {
            const resultDiv = document.createElement('div');
            resultDiv.className = `function-result ${result.success ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <strong>执行结果:</strong><br>
                <pre>${JSON.stringify(result, null, 2)}</pre>
            `;
            lastMessage.appendChild(resultDiv);
        }

        scrollToBottom();
    }

    // 显示打字指示器
    function showTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'flex';
        scrollToBottom();
    }

    // 隐藏打字指示器
    function hideTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'none';
    }

    // 滚动到底部
    function scrollToBottom() {
        const messagesContainer = document.getElementById('chatMessages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // 获取CSRF Token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10 col-xl-8">
            <div class="chat-container">
                <!-- Chat Header -->
                <div class="chat-header">
                    <h3><i class="fas fa-robot me-2"></i>MTC Agent</h3>
                    <p>智能生物计算助手 - 基于千问大模型</p>
                </div>
                
                <!-- Chat Messages -->
                <div class="chat-messages" id="chatMessages">
                    {% if messages %}
                        {% for message in messages %}
                        <div class="message {{ message.role }}">
                            {% if message.role == 'assistant' %}
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            {% endif %}
                            
                            <div class="message-content">
                                {{ message.content|linebreaks }}
                                
                                {% if message.function_result %}
                                <div class="function-result {% if message.function_result.success %}success{% else %}error{% endif %}">
                                    <strong>函数执行结果:</strong><br>
                                    {{ message.function_result|pprint }}
                                </div>
                                {% endif %}
                                
                                <div class="message-time">
                                    {{ message.timestamp|date:"H:i" }}
                                </div>
                            </div>
                            
                            {% if message.role == 'user' %}
                            <div class="message-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="welcome-message">
                            <h4>欢迎使用MTC Agent！</h4>
                            <p>我是您的智能生物计算助手，可以帮您进行蛋白质kcat计算等生物信息学分析。</p>
                            <p>您可以上传文件并描述您的需求，我会自动为您执行相应的计算任务。</p>
                            
                            <div class="sample-questions">
                                <div class="sample-question" onclick="sendSampleQuestion('请帮我计算蛋白质的kcat值')">
                                    计算蛋白质kcat值
                                </div>
                                <div class="sample-question" onclick="sendSampleQuestion('如何准备输入文件？')">
                                    如何准备输入文件？
                                </div>
                                <div class="sample-question" onclick="sendSampleQuestion('支持哪些文件格式？')">
                                    支持哪些文件格式？
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    
                    <!-- Typing Indicator -->
                    <div class="message assistant" id="typingIndicator">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="typing-indicator">
                            <div class="typing-dots">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Chat Input Area -->
                <div class="chat-input-area">
                    <!-- File Upload Area -->
                    <div class="file-upload-area" id="fileUploadArea">
                        <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                        <p class="mb-1">拖拽文件到此处或点击上传</p>
                        <small class="text-muted">支持 JSON (化合物数据) 和 TXT (蛋白质序列) 文件</small>
                        <input type="file" id="fileInput" multiple accept=".json,.txt" style="display: none;">
                        
                        <div class="uploaded-files" id="uploadedFiles"></div>
                    </div>
                    
                    <!-- Message Input -->
                    <div class="input-group">
                        <input type="text" class="form-control" id="messageInput" 
                               placeholder="输入您的问题或需求..." 
                               onkeypress="handleKeyPress(event)">
                        <button class="send-button" id="sendButton" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
