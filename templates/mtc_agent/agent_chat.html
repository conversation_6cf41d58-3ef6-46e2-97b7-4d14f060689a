{% extends 'base.html' %}
{% load static %}

{% block title %}MTC Agent{% endblock %}

{% block extra_css %}
<style>
    .chat-container {
        height: calc(100vh - 150px);
        display: flex;
        flex-direction: column;
    }

    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 1rem 0;
        background: #ffffff;
    }

    .message {
        margin-bottom: 1.5rem;
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .message.user {
        flex-direction: row-reverse;
    }

    .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        flex-shrink: 0;
    }

    .message.user .message-avatar {
        background: #007bff;
        color: white;
    }

    .message.assistant .message-avatar {
        background: #10a37f;
        color: white;
    }

    .message-content {
        max-width: 70%;
        padding: 0.75rem 1rem;
        border-radius: 1rem;
        word-wrap: break-word;
        line-height: 1.5;
    }

    .message.user .message-content {
        background: #007bff;
        color: white;
    }

    .message.assistant .message-content {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
    }

    .message-time {
        font-size: 0.75rem;
        color: #6c757d;
        margin-top: 0.5rem;
    }

    .chat-input-area {
        padding: 1rem;
        background: white;
        border-top: 1px solid #e9ecef;
    }

    .input-container {
        position: relative;
        max-width: 768px;
        margin: 0 auto;
    }

    .file-upload-btn {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        font-size: 1.25rem;
        cursor: pointer;
        z-index: 10;
    }

    .file-upload-btn:hover {
        color: #007bff;
    }

    .message-input {
        padding: 12px 60px 12px 45px;
        border: 1px solid #d1d5db;
        border-radius: 24px;
        resize: none;
        min-height: 48px;
        max-height: 200px;
        font-size: 1rem;
        line-height: 1.5;
    }

    .message-input:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .send-btn {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #007bff;
        border: none;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .send-btn:disabled {
        background: #6c757d;
        cursor: not-allowed;
    }

    .uploaded-files {
        margin-bottom: 0.5rem;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .file-tag {
        background: #e9ecef;
        padding: 0.25rem 0.5rem;
        border-radius: 1rem;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .file-tag.json {
        background: #d4edda;
        color: #155724;
    }

    .file-tag.txt {
        background: #d1ecf1;
        color: #0c5460;
    }

    .file-tag .remove-file {
        cursor: pointer;
        color: #dc3545;
        margin-left: 0.25rem;
        font-weight: bold;
    }

    .typing-indicator {
        display: none;
        padding: 0.75rem 1rem;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 1rem;
        max-width: 70%;
    }

    .typing-dots {
        display: flex;
        gap: 0.25rem;
        align-items: center;
    }

    .typing-dot {
        width: 8px;
        height: 8px;
        background: #10a37f;
        border-radius: 50%;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) { animation-delay: -0.32s; }
    .typing-dot:nth-child(2) { animation-delay: -0.16s; }
    .typing-dot:nth-child(3) { animation-delay: 0s; }

    @keyframes typing {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
    }

    .result-link {
        display: inline-block;
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        margin: 0.5rem 0;
        text-decoration: none;
        color: #1976d2;
        transition: all 0.2s;
    }

    .result-link:hover {
        background: #bbdefb;
        text-decoration: none;
        color: #1565c0;
    }

    .result-link i {
        margin-right: 0.5rem;
    }

    .welcome-message {
        text-align: center;
        padding: 3rem 2rem;
        color: #6c757d;
    }

    .welcome-message h4 {
        color: #495057;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let sessionId = '{{ session.id }}';
    let uploadedFiles = [];

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        setupFileUpload();
        setupInputHandlers();
        scrollToBottom();
        document.getElementById('messageInput').focus();
    });

    // 设置文件上传
    function setupFileUpload() {
        const fileUploadBtn = document.getElementById('fileUploadBtn');
        const fileInput = document.getElementById('fileInput');

        // 点击上传按钮
        fileUploadBtn.addEventListener('click', function() {
            fileInput.click();
        });

        // 文件选择
        fileInput.addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });
    }

    // 设置输入处理
    function setupInputHandlers() {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        // 监听输入变化
        messageInput.addEventListener('input', function() {
            const hasText = this.value.trim().length > 0;
            const hasFiles = uploadedFiles.length > 0;
            sendButton.disabled = !(hasText || hasFiles);
        });
    }

    // 处理文件
    function handleFiles(files) {
        for (let file of files) {
            if (file.type === 'application/json' || file.name.endsWith('.json')) {
                addFile(file, 'json');
            } else if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
                addFile(file, 'txt');
            } else {
                alert('只支持 JSON 和 TXT 文件格式');
            }
        }
        updateFileUploadArea();
    }

    // 添加文件
    function addFile(file, type) {
        const fileObj = {
            file: file,
            name: file.name,
            type: type,
            size: file.size
        };

        uploadedFiles.push(fileObj);
        displayUploadedFiles();
    }

    // 显示已上传文件
    function displayUploadedFiles() {
        const container = document.getElementById('uploadedFiles');
        container.innerHTML = '';

        uploadedFiles.forEach((fileObj, index) => {
            const fileTag = document.createElement('div');
            fileTag.className = `file-tag ${fileObj.type}`;
            fileTag.innerHTML = `
                <i class="fas fa-file-${fileObj.type === 'json' ? 'code' : 'alt'}"></i>
                ${fileObj.name}
                <span class="remove-file" onclick="removeFile(${index})">&times;</span>
            `;
            container.appendChild(fileTag);
        });

        updateFileUploadArea();
    }

    // 移除文件
    function removeFile(index) {
        uploadedFiles.splice(index, 1);
        displayUploadedFiles();
    }

    // 处理键盘事件
    function handleKeyDown(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendMessage();
        }
    }

    // 自动调整文本框高度
    function autoResize(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
    }

    // 发送示例问题
    function sendSampleQuestion(question) {
        document.getElementById('messageInput').value = question;
        sendMessage();
    }

    // 发送消息
    async function sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const message = messageInput.value.trim();

        if (!message && uploadedFiles.length === 0) {
            return;
        }

        // 禁用输入
        messageInput.disabled = true;
        sendButton.disabled = true;

        // 显示用户消息
        if (message) {
            addMessage('user', message);
        }

        // 显示文件信息
        if (uploadedFiles.length > 0) {
            const fileInfo = uploadedFiles.map(f => `📎 ${f.name}`).join(', ');
            addMessage('user', `已上传文件: ${fileInfo}`);
        }

        // 清空输入
        messageInput.value = '';
        messageInput.style.height = 'auto';

        // 显示打字指示器
        showTypingIndicator();

        try {
            // 准备文件数据
            const filesData = await Promise.all(
                uploadedFiles.map(async (fileObj) => {
                    const content = await readFileContent(fileObj.file);
                    return {
                        name: fileObj.name,
                        type: fileObj.type,
                        content: content
                    };
                })
            );

            // 发送请求
            const response = await fetch('{% url "mtc_agent:agent_chat_api" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    message: message,
                    session_id: sessionId,
                    files: filesData
                })
            });

            const data = await response.json();

            if (data.success) {
                sessionId = data.session_id;
                addMessage('assistant', data.message, data.function_result);
            } else {
                addMessage('assistant', `抱歉，出现了错误：${data.error}`);
            }

        } catch (error) {
            console.error('发送消息失败:', error);
            addMessage('assistant', '抱歉，网络连接出现问题，请稍后重试。');
        } finally {
            // 隐藏打字指示器
            hideTypingIndicator();

            // 重新启用输入
            messageInput.disabled = false;
            sendButton.disabled = false;
            messageInput.focus();

            // 清空上传的文件
            uploadedFiles = [];
            displayUploadedFiles();
            updateFileUploadArea();
        }
    }

    // 读取文件内容
    function readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    }

    // 添加消息到聊天界面
    function addMessage(role, content, functionResult = null) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;

        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        let resultLinks = '';
        if (functionResult) {
            if (functionResult.success && functionResult.download_url) {
                resultLinks = `<a href="${functionResult.download_url}" class="result-link" target="_blank">
                    <i class="fas fa-download"></i>下载计算结果
                </a>`;
            } else if (functionResult.task_id) {
                resultLinks = `<a href="/mtc_agent/dlkcat/result/${functionResult.task_id}/" class="result-link">
                    <i class="fas fa-eye"></i>查看任务详情
                </a>`;
            }
        }

        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${role === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                ${content.replace(/\n/g, '<br>')}
                ${resultLinks}
                <div class="message-time">${timeString}</div>
            </div>
        `;

        messagesContainer.insertBefore(messageDiv, document.getElementById('typingIndicator'));
        scrollToBottom();
    }

    // 更新文件上传区域状态
    function updateFileUploadArea() {
        const sendButton = document.getElementById('sendButton');
        const messageInput = document.getElementById('messageInput');
        const hasText = messageInput.value.trim().length > 0;
        const hasFiles = uploadedFiles.length > 0;
        sendButton.disabled = !(hasText || hasFiles);
    }

    // 显示打字指示器
    function showTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'flex';
        scrollToBottom();
    }

    // 隐藏打字指示器
    function hideTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'none';
    }

    // 滚动到底部
    function scrollToBottom() {
        const messagesContainer = document.getElementById('chatMessages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // 获取CSRF Token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
{% endblock %}

{% block content %}
<div class="container-fluid h-100">
    <div class="row h-100 justify-content-center">
        <div class="col-12 col-lg-8 col-xl-6">
            <div class="chat-container">
                <!-- Chat Messages -->
                <div class="chat-messages" id="chatMessages">
                    {% if messages %}
                        {% for message in messages %}
                        <div class="message {{ message.role }}">
                            <div class="message-avatar">
                                {% if message.role == 'user' %}
                                    <i class="fas fa-user"></i>
                                {% else %}
                                    <i class="fas fa-robot"></i>
                                {% endif %}
                            </div>
                            <div class="message-content">
                                {{ message.content|linebreaks }}

                                {% if message.function_result %}
                                    {% if message.function_result.success and message.function_result.download_url %}
                                        <a href="{{ message.function_result.download_url }}" class="result-link" target="_blank">
                                            <i class="fas fa-download"></i>下载计算结果
                                        </a>
                                    {% elif message.function_result.task_id %}
                                        <a href="{% url 'mtc_agent:dlkcat_result' message.function_result.task_id %}" class="result-link">
                                            <i class="fas fa-eye"></i>查看任务详情
                                        </a>
                                    {% endif %}
                                {% endif %}

                                <div class="message-time">
                                    {{ message.timestamp|date:"H:i" }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="welcome-message">
                            <h4>👋 欢迎使用MTC Agent</h4>
                            <p>我是您的智能生物计算助手，可以帮您进行蛋白质kcat计算等分析。</p>
                            <p>您可以上传文件并描述您的需求，我会自动为您执行相应的计算任务。</p>
                        </div>
                    {% endif %}

                    <!-- Typing Indicator -->
                    <div class="message assistant" id="typingIndicator">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="typing-indicator">
                            <div class="typing-dots">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Input Area -->
                <div class="chat-input-area">
                    <div class="input-container">
                        <!-- Uploaded Files Display -->
                        <div class="uploaded-files" id="uploadedFiles"></div>

                        <!-- Input Box -->
                        <div style="position: relative;">
                            <button class="file-upload-btn" id="fileUploadBtn" title="上传文件">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <textarea class="form-control message-input" id="messageInput"
                                     placeholder="输入消息..."
                                     rows="1"
                                     onkeydown="handleKeyDown(event)"
                                     oninput="autoResize(this)"></textarea>
                            <button class="send-btn" id="sendButton" onclick="sendMessage()" disabled>
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>

                        <!-- Hidden File Input -->
                        <input type="file" id="fileInput" multiple accept=".json,.txt" style="display: none;">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
