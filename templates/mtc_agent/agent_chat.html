{% extends 'base.html' %}
{% load static %}

{% block title %}MTC Agent{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    body {
        background: #f7f7f8;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .chat-container {
        height: calc(100vh - 120px);
        max-width: 768px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .chat-header {
        padding: 1rem;
        border-bottom: 1px solid #e5e5e5;
        background: white;
        border-radius: 8px 8px 0 0;
    }

    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        background: white;
    }

    .message {
        margin-bottom: 1.5rem;
        display: flex;
        align-items: flex-start;
        gap: 12px;
    }

    .message.user {
        flex-direction: row-reverse;
    }

    .message-avatar {
        width: 30px;
        height: 30px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        flex-shrink: 0;
        font-weight: 600;
    }

    .message.user .message-avatar {
        background: #19c37d;
        color: white;
    }

    .message.assistant .message-avatar {
        background: #ab68ff;
        color: white;
    }

    .message-content {
        max-width: calc(100% - 50px);
        padding: 12px 16px;
        border-radius: 18px;
        word-wrap: break-word;
        line-height: 1.5;
        font-size: 15px;
    }

    .message.user .message-content {
        background: #2f2f2f;
        color: white;
        margin-left: auto;
    }

    .message.assistant .message-content {
        background: #f4f4f4;
        color: #2f2f2f;
    }

    .message-time {
        font-size: 11px;
        color: #8e8ea0;
        margin-top: 4px;
        text-align: right;
    }

    .message.assistant .message-time {
        text-align: left;
    }

    .chat-input-area {
        padding: 20px;
        background: white;
        border-top: 1px solid #e5e5e5;
        border-radius: 0 0 8px 8px;
    }

    .input-container {
        position: relative;
        background: #f4f4f4;
        border-radius: 26px;
        border: 1px solid #d9d9e3;
        transition: border-color 0.2s;
    }

    .input-container:focus-within {
        border-color: #19c37d;
        box-shadow: 0 0 0 2px rgba(25, 195, 125, 0.2);
    }

    .file-upload-btn {
        position: absolute;
        left: 16px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #8e8ea0;
        font-size: 18px;
        cursor: pointer;
        z-index: 10;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: all 0.2s;
    }

    .file-upload-btn:hover {
        color: #19c37d;
        background: rgba(25, 195, 125, 0.1);
    }

    .message-input {
        width: 100%;
        padding: 12px 50px 12px 50px;
        border: none;
        background: transparent;
        resize: none;
        min-height: 24px;
        max-height: 200px;
        font-size: 16px;
        line-height: 1.5;
        outline: none;
        font-family: inherit;
    }

    .message-input::placeholder {
        color: #8e8ea0;
    }

    .send-btn {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        width: 32px;
        height: 32px;
        border-radius: 16px;
        background: #19c37d;
        border: none;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 14px;
    }

    .send-btn:hover:not(:disabled) {
        background: #16a571;
        transform: translateY(-50%) scale(1.05);
    }

    .send-btn:disabled {
        background: #d9d9e3;
        cursor: not-allowed;
        transform: translateY(-50%);
    }

    .uploaded-files {
        margin-bottom: 12px;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .file-tag {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 13px;
        display: flex;
        align-items: center;
        gap: 6px;
        color: #1976d2;
    }

    .file-tag.json {
        background: #e8f5e8;
        border-color: #4caf50;
        color: #2e7d32;
    }

    .file-tag.txt {
        background: #fff3e0;
        border-color: #ff9800;
        color: #f57c00;
    }

    .file-tag .remove-file {
        cursor: pointer;
        color: #f44336;
        margin-left: 4px;
        font-weight: bold;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background 0.2s;
    }

    .file-tag .remove-file:hover {
        background: rgba(244, 67, 54, 0.1);
    }

    .typing-indicator {
        display: none;
        padding: 12px 16px;
        background: #f4f4f4;
        border-radius: 18px;
        max-width: calc(100% - 50px);
    }

    .typing-dots {
        display: flex;
        gap: 4px;
        align-items: center;
    }

    .typing-dot {
        width: 8px;
        height: 8px;
        background: #8e8ea0;
        border-radius: 50%;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) { animation-delay: -0.32s; }
    .typing-dot:nth-child(2) { animation-delay: -0.16s; }
    .typing-dot:nth-child(3) { animation-delay: 0s; }

    @keyframes typing {
        0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
    }

    .result-link {
        display: inline-flex;
        align-items: center;
        background: #f0f9ff;
        border: 1px solid #0ea5e9;
        border-radius: 8px;
        padding: 8px 12px;
        margin: 8px 0;
        text-decoration: none;
        color: #0369a1;
        transition: all 0.2s;
        font-size: 14px;
        gap: 6px;
    }

    .result-link:hover {
        background: #e0f2fe;
        text-decoration: none;
        color: #0c4a6e;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .welcome-message {
        text-align: center;
        padding: 60px 20px;
        color: #8e8ea0;
    }

    .welcome-message h4 {
        color: #2f2f2f;
        margin-bottom: 16px;
        font-weight: 600;
    }

    .welcome-message p {
        margin-bottom: 12px;
        line-height: 1.6;
    }

    /* 滚动条样式 */
    .chat-messages::-webkit-scrollbar {
        width: 6px;
    }

    .chat-messages::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    .chat-messages::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    .chat-messages::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let sessionId = '{{ session.id }}';
    let uploadedFiles = [];

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializeChat();
    });

    function initializeChat() {
        setupFileUpload();
        setupInputHandlers();
        scrollToBottom();
        document.getElementById('messageInput').focus();
    }

    // 设置文件上传
    function setupFileUpload() {
        const fileUploadBtn = document.getElementById('fileUploadBtn');
        const fileInput = document.getElementById('fileInput');

        fileUploadBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => handleFiles(e.target.files));
    }

    // 设置输入处理
    function setupInputHandlers() {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        // 自动调整高度
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 200) + 'px';

            const hasText = this.value.trim().length > 0;
            const hasFiles = uploadedFiles.length > 0;
            sendButton.disabled = !(hasText || hasFiles);
        });

        // 键盘事件
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 发送按钮
        sendButton.addEventListener('click', sendMessage);
    }

    // 处理文件
    function handleFiles(files) {
        Array.from(files).forEach(file => {
            if (file.name.endsWith('.json')) {
                addFile(file, 'json');
            } else if (file.name.endsWith('.txt')) {
                addFile(file, 'txt');
            } else {
                showAlert('只支持 JSON 和 TXT 文件格式', 'warning');
            }
        });
    }

    // 添加文件
    function addFile(file, type) {
        const fileObj = { file, name: file.name, type, size: file.size };
        uploadedFiles.push(fileObj);
        displayUploadedFiles();
    }

    // 显示已上传文件
    function displayUploadedFiles() {
        const container = document.getElementById('uploadedFiles');
        container.innerHTML = '';

        uploadedFiles.forEach((fileObj, index) => {
            const fileTag = document.createElement('div');
            fileTag.className = `file-tag ${fileObj.type}`;
            fileTag.innerHTML = `
                <i class="fas fa-file-${fileObj.type === 'json' ? 'code' : 'alt'}"></i>
                ${fileObj.name}
                <span class="remove-file" onclick="removeFile(${index})">&times;</span>
            `;
            container.appendChild(fileTag);
        });

        updateSendButton();
    }

    // 移除文件
    function removeFile(index) {
        uploadedFiles.splice(index, 1);
        displayUploadedFiles();
    }

    // 更新发送按钮状态
    function updateSendButton() {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const hasText = messageInput.value.trim().length > 0;
        const hasFiles = uploadedFiles.length > 0;
        sendButton.disabled = !(hasText || hasFiles);
    }

    // 发送消息
    async function sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const message = messageInput.value.trim();

        if (!message && uploadedFiles.length === 0) return;

        // 禁用输入
        setInputState(false);

        // 显示用户消息
        if (message) {
            addMessage('user', message);
        }

        // 显示文件信息
        if (uploadedFiles.length > 0) {
            const fileInfo = uploadedFiles.map(f => `📎 ${f.name}`).join(', ');
            addMessage('user', `已上传文件: ${fileInfo}`);
        }

        // 清空输入
        messageInput.value = '';
        messageInput.style.height = 'auto';

        // 显示打字指示器
        showTypingIndicator();

        try {
            // 准备文件数据
            const filesData = await Promise.all(
                uploadedFiles.map(async (fileObj) => ({
                    name: fileObj.name,
                    type: fileObj.type,
                    content: await readFileContent(fileObj.file)
                }))
            );

            // 发送请求
            const response = await fetch('{% url "mtc_agent:agent_chat_api" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    message: message,
                    session_id: sessionId,
                    files: filesData
                })
            });

            const data = await response.json();

            if (data.success) {
                sessionId = data.session_id;
                addMessage('assistant', data.message, data.function_result);
            } else {
                addMessage('assistant', `抱歉，出现了错误：${data.error}`);
            }

        } catch (error) {
            console.error('发送消息失败:', error);
            addMessage('assistant', '抱歉，网络连接出现问题，请稍后重试。');
        } finally {
            hideTypingIndicator();
            setInputState(true);
            uploadedFiles = [];
            displayUploadedFiles();
        }
    }

        try {
            // 准备文件数据
            const filesData = await Promise.all(
                uploadedFiles.map(async (fileObj) => {
                    const content = await readFileContent(fileObj.file);
                    return {
                        name: fileObj.name,
                        type: fileObj.type,
                        content: content
                    };
                })
            );

            // 发送请求
            const response = await fetch('{% url "mtc_agent:agent_chat_api" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    message: message,
                    session_id: sessionId,
                    files: filesData
                })
            });

            const data = await response.json();

            if (data.success) {
                sessionId = data.session_id;
                addMessage('assistant', data.message, data.function_result);
            } else {
                addMessage('assistant', `抱歉，出现了错误：${data.error}`);
            }

        } catch (error) {
            console.error('发送消息失败:', error);
            addMessage('assistant', '抱歉，网络连接出现问题，请稍后重试。');
        } finally {
            // 隐藏打字指示器
            hideTypingIndicator();

            // 重新启用输入
            messageInput.disabled = false;
            sendButton.disabled = false;
            messageInput.focus();

            // 清空上传的文件
            uploadedFiles = [];
            displayUploadedFiles();
            updateFileUploadArea();
        }
    }

    // 读取文件内容
    function readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    }

    // 添加消息到聊天界面
    function addMessage(role, content, functionResult = null) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;

        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        let resultLinks = '';
        if (functionResult) {
            if (functionResult.success && functionResult.download_url) {
                resultLinks = `<a href="${functionResult.download_url}" class="result-link" target="_blank">
                    <i class="fas fa-download"></i>下载计算结果
                </a>`;
            } else if (functionResult.task_id) {
                resultLinks = `<a href="/mtc_agent/dlkcat/result/${functionResult.task_id}/" class="result-link">
                    <i class="fas fa-eye"></i>查看任务详情
                </a>`;
            }
        }

        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${role === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                ${content.replace(/\n/g, '<br>')}
                ${resultLinks}
                <div class="message-time">${timeString}</div>
            </div>
        `;

        messagesContainer.insertBefore(messageDiv, document.getElementById('typingIndicator'));
        scrollToBottom();
    }

    // 更新文件上传区域状态
    function updateFileUploadArea() {
        const sendButton = document.getElementById('sendButton');
        const messageInput = document.getElementById('messageInput');
        const hasText = messageInput.value.trim().length > 0;
        const hasFiles = uploadedFiles.length > 0;
        sendButton.disabled = !(hasText || hasFiles);
    }

    // 显示打字指示器
    function showTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'flex';
        scrollToBottom();
    }

    // 隐藏打字指示器
    function hideTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'none';
    }

    // 滚动到底部
    function scrollToBottom() {
        const messagesContainer = document.getElementById('chatMessages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // 获取CSRF Token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
{% endblock %}

{% block content %}
<div class="container-fluid py-3">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="chat-container">
                <!-- Chat Header -->
                <div class="chat-header">
                    <h5 class="mb-0 d-flex align-items-center">
                        <i class="fas fa-robot me-2 text-primary"></i>
                        MTC Agent
                        <small class="text-muted ms-2">智能生物计算助手</small>
                    </h5>
                </div>

                <!-- Chat Messages -->
                <div class="chat-messages" id="chatMessages">
                    {% if messages %}
                        {% for message in messages %}
                        <div class="message {{ message.role }}">
                            <div class="message-avatar">
                                {% if message.role == 'user' %}
                                    U
                                {% else %}
                                    AI
                                {% endif %}
                            </div>
                            <div class="message-content">
                                {{ message.content|linebreaks }}

                                {% if message.function_result %}
                                    {% if message.function_result.success and message.function_result.download_url %}
                                        <a href="{{ message.function_result.download_url }}" class="result-link" target="_blank">
                                            <i class="fas fa-download"></i>下载计算结果
                                        </a>
                                    {% elif message.function_result.task_id %}
                                        <a href="{% url 'mtc_agent:dlkcat_result' message.function_result.task_id %}" class="result-link">
                                            <i class="fas fa-eye"></i>查看任务详情
                                        </a>
                                    {% endif %}
                                {% endif %}

                                <div class="message-time">
                                    {{ message.timestamp|date:"H:i" }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="welcome-message">
                            <h4>👋 欢迎使用MTC Agent</h4>
                            <p>我是您的智能生物计算助手，可以帮您进行蛋白质kcat计算等分析。</p>
                            <p>您可以上传文件并描述您的需求，我会自动为您执行相应的计算任务。</p>
                        </div>
                    {% endif %}

                    <!-- Typing Indicator -->
                    <div class="message assistant" id="typingIndicator" style="display: none;">
                        <div class="message-avatar">AI</div>
                        <div class="typing-indicator">
                            <div class="typing-dots">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Input Area -->
                <div class="chat-input-area">
                    <!-- Uploaded Files Display -->
                    <div class="uploaded-files" id="uploadedFiles"></div>

                    <!-- Input Container -->
                    <div class="input-container">
                        <button class="file-upload-btn" id="fileUploadBtn" title="上传文件">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <textarea class="message-input" id="messageInput"
                                 placeholder="输入消息..."
                                 rows="1"></textarea>
                        <button class="send-btn" id="sendButton" disabled>
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>

                    <!-- Hidden File Input -->
                    <input type="file" id="fileInput" multiple accept=".json,.txt" style="display: none;">
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
