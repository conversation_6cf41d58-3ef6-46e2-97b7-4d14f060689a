{% extends 'base.html' %}
{% load static %}

{% block title %}MTC Agent - 智能对话{% endblock %}

{% block extra_css %}
<style>
    .chat-container {
        height: calc(100vh - 200px);
        min-height: 600px;
    }

    .chat-header {
        background: linear-gradient(135deg, #007bff 0%, #6f42c1 100%);
        color: white;
        padding: 1rem;
        border-radius: 0.375rem 0.375rem 0 0;
    }

    .chat-messages {
        height: 400px;
        overflow-y: auto;
        padding: 1rem;
        background: #f8f9fa;
        border-left: 1px solid #dee2e6;
        border-right: 1px solid #dee2e6;
    }

    .message {
        margin-bottom: 1rem;
    }

    .message.user .message-content {
        background: #007bff;
        color: white;
        margin-left: auto;
        margin-right: 0;
        max-width: 70%;
    }

    .message.assistant .message-content {
        background: white;
        border: 1px solid #dee2e6;
        max-width: 70%;
    }

    .message-content {
        padding: 0.75rem 1rem;
        border-radius: 1rem;
        word-wrap: break-word;
    }

    .message-time {
        font-size: 0.75rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    .chat-input-area {
        padding: 1rem;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0 0 0.375rem 0.375rem;
    }

    .file-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        text-align: center;
        margin-bottom: 1rem;
        cursor: pointer;
    }

    .file-upload-area:hover {
        border-color: #007bff;
        background-color: #f8f9ff;
    }

    .file-upload-area.has-files {
        border-color: #28a745;
        background-color: #f8fff9;
    }

    .uploaded-files {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .file-tag {
        background: #e9ecef;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .file-tag.json {
        background: #d4edda;
        color: #155724;
    }

    .file-tag.txt {
        background: #d1ecf1;
        color: #0c5460;
    }

    .file-tag .remove-file {
        cursor: pointer;
        color: #dc3545;
        margin-left: 0.25rem;
    }

    .typing-indicator {
        display: none;
        padding: 0.75rem 1rem;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 1rem;
        max-width: 70%;
    }

    .typing-dots {
        display: flex;
        gap: 0.25rem;
    }

    .typing-dot {
        width: 6px;
        height: 6px;
        background: #007bff;
        border-radius: 50%;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) { animation-delay: -0.32s; }
    .typing-dot:nth-child(2) { animation-delay: -0.16s; }

    @keyframes typing {
        0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
        40% { transform: scale(1); opacity: 1; }
    }

    .function-result {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin-top: 0.5rem;
        font-family: monospace;
        font-size: 0.875rem;
    }

    .function-result.success {
        border-color: #28a745;
        background: #d4edda;
    }

    .function-result.error {
        border-color: #dc3545;
        background: #f8d7da;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let sessionId = '{{ session.id }}';
    let uploadedFiles = [];

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        setupFileUpload();
        scrollToBottom();
        document.getElementById('messageInput').focus();
    });

    // 设置文件上传
    function setupFileUpload() {
        const fileUploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('fileInput');

        // 点击上传
        fileUploadArea.addEventListener('click', function(e) {
            if (e.target === fileUploadArea || e.target.closest('.file-upload-area')) {
                fileInput.click();
            }
        });

        // 文件选择
        fileInput.addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });

        // 拖拽上传
        fileUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        });

        fileUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
        });

        fileUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });
    }

    // 处理文件
    function handleFiles(files) {
        for (let file of files) {
            if (file.type === 'application/json' || file.name.endsWith('.json')) {
                addFile(file, 'json');
            } else if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
                addFile(file, 'txt');
            } else {
                alert('只支持 JSON 和 TXT 文件格式');
            }
        }
        updateFileUploadArea();
    }

    // 添加文件
    function addFile(file, type) {
        const fileObj = {
            file: file,
            name: file.name,
            type: type,
            size: file.size
        };

        uploadedFiles.push(fileObj);
        displayUploadedFiles();
    }

    // 显示已上传文件
    function displayUploadedFiles() {
        const container = document.getElementById('uploadedFiles');
        container.innerHTML = '';

        uploadedFiles.forEach((fileObj, index) => {
            const fileTag = document.createElement('div');
            fileTag.className = `file-tag ${fileObj.type}`;
            fileTag.innerHTML = `
                <i class="fas fa-file-${fileObj.type === 'json' ? 'code' : 'alt'}"></i>
                ${fileObj.name}
                <span class="remove-file" onclick="removeFile(${index})">&times;</span>
            `;
            container.appendChild(fileTag);
        });
    }

    // 移除文件
    function removeFile(index) {
        uploadedFiles.splice(index, 1);
        displayUploadedFiles();
        updateFileUploadArea();
    }

    // 更新文件上传区域状态
    function updateFileUploadArea() {
        const fileUploadArea = document.getElementById('fileUploadArea');
        if (uploadedFiles.length > 0) {
            fileUploadArea.classList.add('has-files');
        } else {
            fileUploadArea.classList.remove('has-files');
        }
    }

    // 处理键盘事件
    function handleKeyPress(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            sendMessage();
        }
    }

    // 发送示例问题
    function sendSampleQuestion(question) {
        document.getElementById('messageInput').value = question;
        sendMessage();
    }

    // 发送消息
    async function sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const message = messageInput.value.trim();

        if (!message && uploadedFiles.length === 0) {
            return;
        }

        // 禁用输入
        messageInput.disabled = true;
        sendButton.disabled = true;

        // 显示用户消息
        if (message) {
            addMessage('user', message);
        }

        // 显示文件信息
        if (uploadedFiles.length > 0) {
            const fileInfo = uploadedFiles.map(f => `📎 ${f.name}`).join('\n');
            addMessage('user', `上传文件:\n${fileInfo}`);
        }

        // 清空输入
        messageInput.value = '';

        // 显示打字指示器
        showTypingIndicator();

        try {
            // 准备文件数据
            const filesData = await Promise.all(
                uploadedFiles.map(async (fileObj) => {
                    const content = await readFileContent(fileObj.file);
                    return {
                        name: fileObj.name,
                        type: fileObj.type,
                        content: content
                    };
                })
            );

            // 发送请求
            const response = await fetch('{% url "mtc_agent:agent_chat_api" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    message: message,
                    session_id: sessionId,
                    files: filesData
                })
            });

            const data = await response.json();

            if (data.success) {
                sessionId = data.session_id;
                addMessage('assistant', data.message);

                // 显示函数执行结果
                if (data.function_result) {
                    addFunctionResult(data.function_result);
                }
            } else {
                addMessage('assistant', `抱歉，出现了错误：${data.error}`);
            }

        } catch (error) {
            console.error('发送消息失败:', error);
            addMessage('assistant', '抱歉，网络连接出现问题，请稍后重试。');
        } finally {
            // 隐藏打字指示器
            hideTypingIndicator();

            // 重新启用输入
            messageInput.disabled = false;
            sendButton.disabled = false;
            messageInput.focus();

            // 清空上传的文件
            uploadedFiles = [];
            displayUploadedFiles();
            updateFileUploadArea();
        }
    }

    // 读取文件内容
    function readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsText(file);
        });
    }

    // 添加消息到聊天界面
    function addMessage(role, content) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;

        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageDiv.innerHTML = `
            <div class="message-content">
                ${content.replace(/\n/g, '<br>')}
                <div class="message-time">${timeString}</div>
            </div>
        `;

        messagesContainer.insertBefore(messageDiv, document.getElementById('typingIndicator'));
        scrollToBottom();
    }

    // 添加函数执行结果
    function addFunctionResult(result) {
        const messagesContainer = document.getElementById('chatMessages');
        const lastMessage = messagesContainer.querySelector('.message.assistant:last-of-type .message-content');

        if (lastMessage) {
            const resultDiv = document.createElement('div');
            resultDiv.className = `function-result ${result.success ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <strong>执行结果:</strong><br>
                <pre>${JSON.stringify(result, null, 2)}</pre>
            `;
            lastMessage.appendChild(resultDiv);
        }

        scrollToBottom();
    }

    // 显示打字指示器
    function showTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'flex';
        scrollToBottom();
    }

    // 隐藏打字指示器
    function hideTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        indicator.style.display = 'none';
    }

    // 滚动到底部
    function scrollToBottom() {
        const messagesContainer = document.getElementById('chatMessages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // 获取CSRF Token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-8">
            <div class="card chat-container">
                <!-- Chat Header -->
                <div class="card-header chat-header">
                    <h5 class="mb-0"><i class="fas fa-robot me-2"></i>MTC Agent</h5>
                </div>

                <!-- Chat Messages -->
                <div class="chat-messages" id="chatMessages">
                    {% if messages %}
                        {% for message in messages %}
                        <div class="message {{ message.role }}">
                            <div class="message-content">
                                {{ message.content|linebreaks }}

                                {% if message.function_result %}
                                <div class="function-result {% if message.function_result.success %}success{% else %}error{% endif %}">
                                    <strong>执行结果:</strong><br>
                                    <pre>{{ message.function_result|pprint }}</pre>
                                </div>
                                {% endif %}

                                <div class="message-time">
                                    {{ message.timestamp|date:"H:i" }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center p-4 text-muted">
                            <h5>欢迎使用MTC Agent</h5>
                            <p>我是您的智能生物计算助手，可以帮您进行蛋白质kcat计算等分析。</p>
                        </div>
                    {% endif %}

                    <!-- Typing Indicator -->
                    <div class="message assistant" id="typingIndicator">
                        <div class="typing-indicator">
                            <div class="typing-dots">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Input Area -->
                <div class="chat-input-area">
                    <!-- File Upload Area -->
                    <div class="file-upload-area" id="fileUploadArea">
                        <i class="fas fa-cloud-upload-alt text-muted"></i>
                        <span class="text-muted ms-2">拖拽文件或点击上传 (JSON/TXT)</span>
                        <input type="file" id="fileInput" multiple accept=".json,.txt" style="display: none;">
                        <div class="uploaded-files" id="uploadedFiles"></div>
                    </div>

                    <!-- Message Input -->
                    <div class="input-group">
                        <input type="text" class="form-control" id="messageInput"
                               placeholder="输入您的问题..."
                               onkeypress="handleKeyPress(event)">
                        <button class="btn btn-primary" id="sendButton" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
