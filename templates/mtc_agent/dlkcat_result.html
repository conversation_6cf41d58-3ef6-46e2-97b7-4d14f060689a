{% extends 'base.html' %}
{% load static %}

{% block title %}DLKcat计算结果 - {{ task.id }}{% endblock %}

{% block extra_css %}
<style>
    .result-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
        margin: 2rem 0;
    }
    
    .result-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
    }
    
    .result-header h2 {
        margin: 0;
        font-weight: 600;
    }
    
    .result-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
    }
    
    .result-content {
        padding: 2rem;
    }
    
    .status-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }
    
    .status-pending {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    .status-processing {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
    
    .status-completed {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .status-failed {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .info-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .info-card h5 {
        color: #495057;
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 500;
        color: #6c757d;
    }
    
    .info-value {
        color: #495057;
        font-family: 'Courier New', monospace;
    }
    
    .download-section {
        text-align: center;
        padding: 2rem;
        background: #f8f9fa;
        border-radius: 10px;
        margin: 2rem 0;
    }
    
    .download-button {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 50px;
        color: white;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    }
    
    .download-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
        color: white;
        text-decoration: none;
    }
    
    .download-button:disabled {
        background: #6c757d;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
    
    .error-message {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 10px;
        padding: 1.5rem;
        color: #721c24;
        margin: 1.5rem 0;
    }
    
    .error-message h5 {
        color: #721c24;
        margin-bottom: 1rem;
    }
    
    .progress-section {
        margin: 2rem 0;
    }
    
    .progress {
        height: 10px;
        border-radius: 5px;
        background: #e9ecef;
        overflow: hidden;
    }
    
    .progress-bar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        height: 100%;
        transition: width 0.3s ease;
    }
    
    .progress-bar.processing {
        animation: progress-animation 2s infinite;
    }
    
    @keyframes progress-animation {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
    
    .file-info {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1rem;
        margin: 0.5rem 0;
    }
    
    .file-info h6 {
        margin: 0 0 0.5rem 0;
        color: #495057;
        font-weight: 600;
    }
    
    .file-info .file-details {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .refresh-button {
        background: #6c757d;
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .refresh-button:hover {
        background: #5a6268;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-10 col-xl-8">
            <div class="result-container">
                <!-- Result Header -->
                <div class="result-header">
                    <h2><i class="fas fa-dna me-2"></i>DLKcat计算结果</h2>
                    <p>任务ID: {{ task.id }}</p>
                </div>
                
                <!-- Result Content -->
                <div class="result-content">
                    <!-- Status Section -->
                    <div class="text-center mb-4">
                        <span class="status-badge status-{{ task.status }}">
                            {% if task.status == 'pending' %}
                                <i class="fas fa-clock me-1"></i>等待中
                            {% elif task.status == 'processing' %}
                                <i class="fas fa-spinner fa-spin me-1"></i>处理中
                            {% elif task.status == 'completed' %}
                                <i class="fas fa-check-circle me-1"></i>已完成
                            {% elif task.status == 'failed' %}
                                <i class="fas fa-times-circle me-1"></i>失败
                            {% endif %}
                        </span>
                    </div>
                    
                    <!-- Progress Bar -->
                    {% if task.status == 'processing' %}
                    <div class="progress-section">
                        <div class="progress">
                            <div class="progress-bar processing" style="width: 100%"></div>
                        </div>
                        <p class="text-center mt-2 text-muted">
                            <i class="fas fa-cog fa-spin me-1"></i>正在计算中，请稍候...
                        </p>
                    </div>
                    {% elif task.status == 'completed' %}
                    <div class="progress-section">
                        <div class="progress">
                            <div class="progress-bar" style="width: 100%"></div>
                        </div>
                        <p class="text-center mt-2 text-success">
                            <i class="fas fa-check me-1"></i>计算已完成
                        </p>
                    </div>
                    {% endif %}
                    
                    <!-- Task Information -->
                    <div class="info-card">
                        <h5><i class="fas fa-info-circle me-2"></i>任务信息</h5>
                        
                        <div class="info-row">
                            <span class="info-label">任务ID:</span>
                            <span class="info-value">{{ task.id }}</span>
                        </div>
                        
                        <div class="info-row">
                            <span class="info-label">创建时间:</span>
                            <span class="info-value">{{ task.created_at|date:"Y-m-d H:i:s" }}</span>
                        </div>
                        
                        <div class="info-row">
                            <span class="info-label">更新时间:</span>
                            <span class="info-value">{{ task.updated_at|date:"Y-m-d H:i:s" }}</span>
                        </div>
                        
                        {% if task.email %}
                        <div class="info-row">
                            <span class="info-label">通知邮箱:</span>
                            <span class="info-value">{{ task.email }}</span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Input Files Information -->
                    <div class="info-card">
                        <h5><i class="fas fa-file me-2"></i>输入文件</h5>
                        
                        {% if task.compounds_file %}
                        <div class="file-info">
                            <h6><i class="fas fa-file-code me-1"></i>化合物数据文件</h6>
                            <div class="file-details">
                                文件名: {{ task.compounds_file.name|default:"compounds.json" }}<br>
                                格式: JSON
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if task.sequences_file %}
                        <div class="file-info">
                            <h6><i class="fas fa-file-alt me-1"></i>蛋白质序列文件</h6>
                            <div class="file-details">
                                文件名: {{ task.sequences_file.name|default:"sequences.txt" }}<br>
                                格式: TXT
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Download Section -->
                    {% if task.status == 'completed' and task.download_url %}
                    <div class="download-section">
                        <h4><i class="fas fa-download me-2"></i>下载结果</h4>
                        <p class="text-muted mb-3">计算已完成，点击下载结果文件</p>
                        <a href="{{ task.download_url }}" class="download-button" target="_blank">
                            <i class="fas fa-download me-2"></i>下载CSV结果文件
                        </a>
                    </div>
                    {% elif task.status == 'processing' %}
                    <div class="download-section">
                        <h4><i class="fas fa-hourglass-half me-2"></i>计算进行中</h4>
                        <p class="text-muted mb-3">请耐心等待计算完成</p>
                        <button class="refresh-button" onclick="location.reload()">
                            <i class="fas fa-sync-alt me-1"></i>刷新状态
                        </button>
                    </div>
                    {% elif task.status == 'pending' %}
                    <div class="download-section">
                        <h4><i class="fas fa-clock me-2"></i>等待处理</h4>
                        <p class="text-muted mb-3">任务已提交，等待系统处理</p>
                        <button class="refresh-button" onclick="location.reload()">
                            <i class="fas fa-sync-alt me-1"></i>刷新状态
                        </button>
                    </div>
                    {% endif %}
                    
                    <!-- Error Message -->
                    {% if task.status == 'failed' %}
                    <div class="error-message">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>计算失败</h5>
                        <p>{{ task.error_message|default:"计算过程中发生未知错误，请联系管理员。" }}</p>
                        <div class="mt-3">
                            <a href="{% url 'mtc_agent:agent_chat' %}" class="btn btn-primary">
                                <i class="fas fa-redo me-1"></i>重新开始
                            </a>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Action Buttons -->
                    <div class="text-center mt-4">
                        <a href="{% url 'mtc_agent:agent_chat' %}" class="btn btn-outline-primary me-2">
                            <i class="fas fa-comments me-1"></i>返回对话
                        </a>
                        <a href="{% url 'mtc_agent:agent_home' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-1"></i>返回首页
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 自动刷新处理中的任务
    {% if task.status == 'processing' or task.status == 'pending' %}
    setTimeout(function() {
        location.reload();
    }, 10000); // 10秒后自动刷新
    {% endif %}
    
    // 复制任务ID功能
    function copyTaskId() {
        const taskId = '{{ task.id }}';
        navigator.clipboard.writeText(taskId).then(function() {
            alert('任务ID已复制到剪贴板');
        });
    }
</script>
{% endblock %}
