{% extends 'base.html' %}
{% load static %}

{% block title %}错误 - MTC Agent{% endblock %}

{% block extra_css %}
<style>
    .error-container {
        min-height: 60vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    
    .error-content {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        padding: 3rem;
        max-width: 600px;
    }
    
    .error-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 1.5rem;
    }
    
    .error-title {
        font-size: 2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1rem;
    }
    
    .error-message {
        color: #666;
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 2rem;
    }
    
    .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-weight: 600;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="error-container">
        <div class="error-content">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <h1 class="error-title">出现错误</h1>
            
            <div class="error-message">
                {{ error_message|default:"抱歉，系统遇到了一个问题。请稍后重试或联系管理员。" }}
            </div>
            
            <div class="error-actions">
                <a href="{% url 'mtc_agent:agent_home' %}" class="btn btn-primary">
                    <i class="fas fa-home me-1"></i>返回首页
                </a>
                <a href="{% url 'mtc_agent:agent_chat' %}" class="btn btn-outline-primary">
                    <i class="fas fa-comments me-1"></i>重新开始对话
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
