<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DLKcat计算失败通知</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        
        .email-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        .header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 2rem;
        }
        
        .error-badge {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 2rem;
            font-weight: 600;
        }
        
        .info-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .info-section h3 {
            margin-top: 0;
            color: #495057;
            font-size: 1.2rem;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #6c757d;
        }
        
        .info-value {
            color: #495057;
            font-family: 'Courier New', monospace;
        }
        
        .error-section {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
        }
        
        .error-section h3 {
            margin-top: 0;
            color: #c53030;
            font-size: 1.2rem;
        }
        
        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            word-break: break-word;
        }
        
        .action-section {
            text-align: center;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 10px;
            margin: 2rem 0;
        }
        
        .action-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            display: inline-block;
            margin: 0.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            color: white;
            text-decoration: none;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 1.5rem;
            text-align: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>⚠️ DLKcat计算失败</h1>
            <p>很抱歉，您的计算任务遇到了问题</p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <!-- Error Message -->
            <div class="error-badge">
                ❌ 计算过程中发生错误，任务未能完成。
            </div>
            
            <!-- Task Information -->
            <div class="info-section">
                <h3>📋 任务信息</h3>
                
                <div class="info-row">
                    <span class="info-label">任务ID:</span>
                    <span class="info-value">{{ task_id }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">开始时间:</span>
                    <span class="info-value">{{ created_at|date:"Y-m-d H:i:s" }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">失败时间:</span>
                    <span class="info-value">{{ failed_at|date:"Y-m-d H:i:s" }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">状态:</span>
                    <span class="info-value">{{ status }}</span>
                </div>
            </div>
            
            <!-- Error Details -->
            {% if error_message %}
            <div class="error-section">
                <h3>🔍 错误详情</h3>
                <div class="error-message">
                    {{ error_message|default:"未知错误，请联系技术支持。" }}
                </div>
            </div>
            {% endif %}
            
            <!-- Action Section -->
            <div class="action-section">
                <h3>🔧 下一步操作</h3>
                <p>您可以尝试以下操作：</p>
                <a href="{{ site_url }}/mtc_agent/agent_chat/" class="action-button">
                    🔄 重新开始计算
                </a>
                <a href="{{ site_url }}/mtc_agent/dlkcat/result/{{ task_id }}/" class="action-button">
                    📋 查看任务详情
                </a>
            </div>
            
            <!-- Help Information -->
            <div class="info-section">
                <h3>💡 常见问题解决方案</h3>
                <ul>
                    <li><strong>文件格式错误：</strong>请确保JSON文件包含所有必需字段（name、Kegg_id、gotenzymes smiles、Isomeric smiles、Canonical smiles）</li>
                    <li><strong>序列格式错误：</strong>请确保TXT文件中每行包含一个有效的蛋白质序列</li>
                    <li><strong>文件大小限制：</strong>请确保上传的文件不超过系统限制</li>
                    <li><strong>网络问题：</strong>如果是网络超时，请稍后重试</li>
                </ul>
                <p>
                    如果问题持续存在，请联系我们的技术支持团队，并提供任务ID：<strong>{{ task_id }}</strong>
                </p>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p>
                感谢使用 <strong>MTC Agent</strong> 智能生物计算助手！<br>
                如需帮助，请访问 <a href="{{ site_url }}">我们的网站</a> 或联系技术支持。
            </p>
            <p>
                <small>
                    此邮件由系统自动发送，请勿直接回复。<br>
                    MTC Lab © {{ failed_at|date:"Y" }}
                </small>
            </p>
        </div>
    </div>
</body>
</html>
