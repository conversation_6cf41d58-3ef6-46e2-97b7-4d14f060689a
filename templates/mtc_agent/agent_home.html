{% extends 'base.html' %}
{% load static %}

{% block title %}MTC Agent - 智能生物计算助手{% endblock %}

{% block extra_css %}
<style>
    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 80px 0;
        text-align: center;
    }
    
    .hero-title {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }
    
    .hero-subtitle {
        font-size: 1.3rem;
        margin-bottom: 2rem;
        opacity: 0.9;
    }
    
    .feature-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
        border: none;
    }
    
    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }
    
    .feature-icon {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 1rem;
    }
    
    .feature-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #333;
    }
    
    .feature-description {
        color: #666;
        line-height: 1.6;
    }
    
    .cta-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 15px 40px;
        font-size: 1.2rem;
        font-weight: 600;
        border-radius: 50px;
        color: white;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .cta-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
        text-decoration: none;
    }
    
    .stats-section {
        background: #f8f9fa;
        padding: 60px 0;
    }
    
    .stat-item {
        text-align: center;
        padding: 2rem;
    }
    
    .stat-number {
        font-size: 3rem;
        font-weight: 700;
        color: #667eea;
        display: block;
    }
    
    .stat-label {
        font-size: 1.1rem;
        color: #666;
        margin-top: 0.5rem;
    }
    
    .workflow-section {
        padding: 80px 0;
    }
    
    .workflow-step {
        text-align: center;
        padding: 2rem;
    }
    
    .workflow-number {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0 auto 1rem;
    }
    
    .workflow-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #333;
    }
    
    .workflow-description {
        color: #666;
        line-height: 1.6;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <h1 class="hero-title">MTC Agent</h1>
        <p class="hero-subtitle">基于千问大模型的智能生物计算助手</p>
        <p class="lead mb-4">专业的蛋白质kcat计算、生物信息学分析，让科研更简单高效</p>
        <a href="{% url 'mtc_agent:agent_chat' %}" class="cta-button">
            <i class="fas fa-comments me-2"></i>开始对话
        </a>
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold mb-3">核心功能</h2>
                <p class="lead text-muted">集成先进的AI技术，提供专业的生物计算服务</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-dna"></i>
                    </div>
                    <h3 class="feature-title">DLKcat计算</h3>
                    <p class="feature-description">
                        使用深度学习方法计算蛋白质的kcat值，支持批量处理化合物和蛋白质序列，
                        提供准确的酶动力学参数预测。
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="feature-title">智能对话</h3>
                    <p class="feature-description">
                        基于千问大模型的智能对话系统，支持自然语言交互，
                        自动理解用户需求并调用相应的计算功能。
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <h3 class="feature-title">多文件上传</h3>
                    <p class="feature-description">
                        支持拖拽上传多个文件，自动识别JSON和TXT格式，
                        智能验证文件内容格式，确保计算准确性。
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="stats-section">
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">1000+</span>
                    <div class="stat-label">计算任务</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">50+</span>
                    <div class="stat-label">研究机构</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">99.5%</span>
                    <div class="stat-label">准确率</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <div class="stat-label">在线服务</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Workflow Section -->
<section class="workflow-section">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold mb-3">使用流程</h2>
                <p class="lead text-muted">简单三步，完成专业的生物计算分析</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4">
                <div class="workflow-step">
                    <div class="workflow-number">1</div>
                    <h3 class="workflow-title">上传文件</h3>
                    <p class="workflow-description">
                        上传化合物JSON文件和蛋白质序列TXT文件，
                        系统会自动验证文件格式和内容。
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="workflow-step">
                    <div class="workflow-number">2</div>
                    <h3 class="workflow-title">智能对话</h3>
                    <p class="workflow-description">
                        通过自然语言描述您的计算需求，
                        AI助手会自动理解并执行相应的计算任务。
                    </p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="workflow-step">
                    <div class="workflow-number">3</div>
                    <h3 class="workflow-title">获取结果</h3>
                    <p class="workflow-description">
                        计算完成后自动生成结果文件，
                        支持在线查看和下载，可选择邮件通知。
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-light">
    <div class="container text-center">
        <h2 class="display-6 fw-bold mb-3">准备开始您的研究？</h2>
        <p class="lead mb-4">立即体验智能生物计算助手，让科研工作更高效</p>
        <a href="{% url 'mtc_agent:agent_chat' %}" class="cta-button me-3">
            <i class="fas fa-play me-2"></i>立即开始
        </a>
        <a href="#" class="btn btn-outline-secondary btn-lg">
            <i class="fas fa-book me-2"></i>查看文档
        </a>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    // 添加一些交互效果
    document.addEventListener('DOMContentLoaded', function() {
        // 统计数字动画
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach(stat => {
            const finalNumber = stat.textContent;
            stat.textContent = '0';
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateNumber(stat, finalNumber);
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe(stat);
        });
    });
    
    function animateNumber(element, finalNumber) {
        const isPercentage = finalNumber.includes('%');
        const isTime = finalNumber.includes('/');
        const isPlus = finalNumber.includes('+');
        
        let numericValue = parseFloat(finalNumber.replace(/[^\d.]/g, ''));
        let current = 0;
        const increment = numericValue / 50;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= numericValue) {
                current = numericValue;
                clearInterval(timer);
            }
            
            let displayValue = Math.floor(current);
            if (isPercentage) displayValue += '%';
            if (isTime) displayValue += '/7';
            if (isPlus) displayValue += '+';
            
            element.textContent = displayValue;
        }, 30);
    }
</script>
{% endblock %}
