import json
import logging
from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import ChatSession, ChatMessage, DLKcatTask, UploadedFile
from .qwen_client import QwenClient
from .dlkcat_km import calculate_dlkcat, get_calculation_status, get_download_url
from .file_utils import FileProcessor, FileValidator
import uuid

logger = logging.getLogger(__name__)

def agent_home(request):
    """Agent主页"""
    return render(request, 'mtc_agent/agent_home.html')

def agent_chat(request):
    """Agent聊天界面"""
    # 创建或获取聊天会话
    session_id = request.GET.get('session_id')
    if session_id:
        try:
            session = ChatSession.objects.get(id=session_id)
        except ChatSession.DoesNotExist:
            session = ChatSession.objects.create(
                user=request.user if request.user.is_authenticated else None
            )
    else:
        session = ChatSession.objects.create(
            user=request.user if request.user.is_authenticated else None
        )

    # 获取历史消息
    messages = session.messages.all()

    context = {
        'session': session,
        'messages': messages,
    }
    return render(request, 'mtc_agent/agent_chat.html', context)

@csrf_exempt
@require_http_methods(["POST"])
def agent_chat_api(request):
    """Agent聊天API"""
    try:
        data = json.loads(request.body)
        message = data.get('message', '')
        session_id = data.get('session_id')
        files_data = data.get('files', [])

        if not message.strip() and not files_data:
            return JsonResponse({
                'error': '请输入消息或上传文件'
            }, status=400)

        # 获取或创建会话
        if session_id:
            try:
                session = ChatSession.objects.get(id=session_id)
            except ChatSession.DoesNotExist:
                session = ChatSession.objects.create(
                    user=request.user if request.user.is_authenticated else None
                )
        else:
            session = ChatSession.objects.create(
                user=request.user if request.user.is_authenticated else None
            )

        # 处理上传的文件
        uploaded_files = []
        processed_files = {}

        if files_data:
            # 验证和处理文件
            is_valid, validation_message, file_results = FileProcessor.process_uploaded_files(files_data)

            if not is_valid:
                return JsonResponse({
                    'error': validation_message
                }, status=400)

            processed_files = file_results

            # 保存文件记录
            for file_data in files_data:
                uploaded_file = UploadedFile.objects.create(
                    session=session,
                    original_name=file_data.get('name', ''),
                    file_type=file_data.get('type', ''),
                )
                uploaded_files.append(uploaded_file)

        # 保存用户消息
        user_message = ChatMessage.objects.create(
            session=session,
            role='user',
            content=message
        )

        # 准备消息历史
        messages = []
        for msg in session.messages.filter(role__in=['user', 'assistant']).order_by('timestamp'):
            messages.append({
                'role': msg.role,
                'content': msg.content
            })

        # 调用千问API
        qwen_client = QwenClient()
        functions = qwen_client.get_available_functions()

        response = qwen_client.chat_completion(
            messages=messages,
            functions=functions,
            function_call="auto"
        )

        if 'error' in response:
            return JsonResponse({
                'error': response['error']
            }, status=500)

        # 处理响应
        choice = response['choices'][0]
        assistant_message = choice['message']

        # 检查是否有函数调用
        if 'function_call' in assistant_message:
            function_call = assistant_message['function_call']
            function_name = function_call['name']
            function_args = json.loads(function_call['arguments'])

            # 执行函数调用
            function_result = qwen_client.process_function_call(
                function_name,
                function_args,
                session_id=str(session.id),
                processed_files=processed_files
            )

            # 保存函数调用消息
            function_message = ChatMessage.objects.create(
                session=session,
                role='assistant',
                content=f"正在执行 {function_name} 函数...",
                function_name=function_name,
                function_args=function_args,
                function_result=function_result
            )

            # 继续对话，包含函数结果
            messages.append({
                'role': 'assistant',
                'content': None,
                'function_call': function_call
            })
            messages.append({
                'role': 'function',
                'name': function_name,
                'content': json.dumps(function_result, ensure_ascii=False)
            })

            # 再次调用API获取最终回复
            final_response = qwen_client.chat_completion(messages=messages)
            if 'error' not in final_response:
                final_choice = final_response['choices'][0]
                final_message = final_choice['message']

                # 保存最终回复
                assistant_reply = ChatMessage.objects.create(
                    session=session,
                    role='assistant',
                    content=final_message['content']
                )

                return JsonResponse({
                    'success': True,
                    'session_id': str(session.id),
                    'message': final_message['content'],
                    'function_result': function_result
                })

        # 普通回复
        assistant_reply = ChatMessage.objects.create(
            session=session,
            role='assistant',
            content=assistant_message['content']
        )

        return JsonResponse({
            'success': True,
            'session_id': str(session.id),
            'message': assistant_message['content']
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'error': '无效的JSON数据'
        }, status=400)
    except Exception as e:
        logger.error(f"聊天API错误: {e}")
        return JsonResponse({
            'error': f'服务器错误: {str(e)}'
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def dlkcat_calculation_api(request):
    """DLKcat计算API"""
    try:
        # 处理文件上传
        compounds_file = request.FILES.get('compounds_file')
        sequences_file = request.FILES.get('sequences_file')
        email = request.POST.get('email', '')
        session_id = request.POST.get('session_id', '')

        if not compounds_file or not sequences_file:
            return JsonResponse({
                'error': '请上传化合物JSON文件和蛋白质序列TXT文件'
            }, status=400)

        # 读取文件内容
        try:
            compounds_content = compounds_file.read().decode('utf-8')
            compounds_data = json.loads(compounds_content)
        except (UnicodeDecodeError, json.JSONDecodeError) as e:
            return JsonResponse({
                'error': f'化合物文件格式错误: {str(e)}'
            }, status=400)

        try:
            sequences_content = sequences_file.read().decode('utf-8')
            sequences = [seq.strip() for seq in sequences_content.split('\n') if seq.strip()]
        except UnicodeDecodeError as e:
            return JsonResponse({
                'error': f'序列文件格式错误: {str(e)}'
            }, status=400)

        # 调用计算函数
        result = calculate_dlkcat(
            compounds_data=compounds_data,
            sequences=sequences,
            email=email if email else None,
            session_id=session_id if session_id else None
        )

        if result['success']:
            return JsonResponse({
                'success': True,
                'task_id': result['task_id'],
                'message': result['message'],
                'download_url': result.get('download_url')
            })
        else:
            return JsonResponse({
                'success': False,
                'error': result['error'],
                'message': result['message']
            }, status=400)

    except Exception as e:
        logger.error(f"DLKcat计算API错误: {e}")
        return JsonResponse({
            'error': f'服务器错误: {str(e)}'
        }, status=500)

def dlkcat_result(request, task_id):
    """DLKcat结果页面"""
    try:
        task = get_object_or_404(DLKcatTask, id=task_id)
        context = {
            'task': task,
        }
        return render(request, 'mtc_agent/dlkcat_result.html', context)
    except Exception as e:
        logger.error(f"获取DLKcat结果错误: {e}")
        return render(request, 'mtc_agent/error.html', {
            'error_message': f'获取结果失败: {str(e)}'
        })

@csrf_exempt
@require_http_methods(["GET"])
def get_task_status_api(request, task_id):
    """获取任务状态API"""
    try:
        result = get_calculation_status(task_id)
        return JsonResponse(result)
    except Exception as e:
        logger.error(f"获取任务状态API错误: {e}")
        return JsonResponse({
            'success': False,
            'error': f'服务器错误: {str(e)}'
        }, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def download_result_api(request, task_id):
    """下载结果API"""
    try:
        result = get_download_url(task_id)
        return JsonResponse(result)
    except Exception as e:
        logger.error(f"下载结果API错误: {e}")
        return JsonResponse({
            'success': False,
            'error': f'服务器错误: {str(e)}'
        }, status=500)
