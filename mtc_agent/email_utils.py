import logging
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils.html import strip_tags
from typing import Optional
from .models import DLKcatTask

logger = logging.getLogger(__name__)

class EmailService:
    """邮件服务类"""
    
    @staticmethod
    def send_calculation_complete_email(task: DLKcatTask) -> bool:
        """发送计算完成邮件"""
        if not task.email:
            logger.info(f"任务 {task.id} 没有设置邮箱，跳过邮件发送")
            return True
        
        try:
            subject = f"MTC Agent - DLKcat计算完成通知 (任务ID: {task.id})"
            
            # 准备邮件内容上下文
            context = {
                'task': task,
                'task_id': task.id,
                'status': task.get_status_display(),
                'created_at': task.created_at,
                'completed_at': task.updated_at,
                'download_url': task.download_url,
                'site_url': getattr(settings, 'SITE_URL', 'http://localhost:8000'),
            }
            
            # 渲染HTML邮件模板
            html_content = render_to_string('mtc_agent/emails/calculation_complete.html', context)
            text_content = strip_tags(html_content)
            
            # 创建邮件
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[task.email]
            )
            email.attach_alternative(html_content, "text/html")
            
            # 发送邮件
            email.send()
            
            logger.info(f"成功发送计算完成邮件到 {task.email} (任务ID: {task.id})")
            return True
            
        except Exception as e:
            logger.error(f"发送邮件失败 (任务ID: {task.id}, 邮箱: {task.email}): {e}")
            return False
    
    @staticmethod
    def send_calculation_failed_email(task: DLKcatTask) -> bool:
        """发送计算失败邮件"""
        if not task.email:
            logger.info(f"任务 {task.id} 没有设置邮箱，跳过邮件发送")
            return True
        
        try:
            subject = f"MTC Agent - DLKcat计算失败通知 (任务ID: {task.id})"
            
            # 准备邮件内容上下文
            context = {
                'task': task,
                'task_id': task.id,
                'status': task.get_status_display(),
                'created_at': task.created_at,
                'failed_at': task.updated_at,
                'error_message': task.error_message,
                'site_url': getattr(settings, 'SITE_URL', 'http://localhost:8000'),
            }
            
            # 渲染HTML邮件模板
            html_content = render_to_string('mtc_agent/emails/calculation_failed.html', context)
            text_content = strip_tags(html_content)
            
            # 创建邮件
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[task.email]
            )
            email.attach_alternative(html_content, "text/html")
            
            # 发送邮件
            email.send()
            
            logger.info(f"成功发送计算失败邮件到 {task.email} (任务ID: {task.id})")
            return True
            
        except Exception as e:
            logger.error(f"发送失败邮件失败 (任务ID: {task.id}, 邮箱: {task.email}): {e}")
            return False
    
    @staticmethod
    def send_task_started_email(task: DLKcatTask) -> bool:
        """发送任务开始邮件"""
        if not task.email:
            return True
        
        try:
            subject = f"MTC Agent - DLKcat计算已开始 (任务ID: {task.id})"
            
            context = {
                'task': task,
                'task_id': task.id,
                'created_at': task.created_at,
                'site_url': getattr(settings, 'SITE_URL', 'http://localhost:8000'),
            }
            
            # 简单的文本邮件
            message = f"""
亲爱的用户，

您的DLKcat计算任务已经开始处理。

任务信息：
- 任务ID: {task.id}
- 开始时间: {task.created_at.strftime('%Y-%m-%d %H:%M:%S')}
- 状态: 处理中

您可以通过以下链接查看任务状态：
{context['site_url']}/mtc_agent/dlkcat/result/{task.id}/

计算完成后我们会再次通知您。

感谢使用MTC Agent！

MTC Lab团队
            """.strip()
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[task.email],
                fail_silently=False
            )
            
            logger.info(f"成功发送任务开始邮件到 {task.email} (任务ID: {task.id})")
            return True
            
        except Exception as e:
            logger.error(f"发送任务开始邮件失败 (任务ID: {task.id}, 邮箱: {task.email}): {e}")
            return False

def send_calculation_notification(task_id: str, status: str) -> bool:
    """发送计算通知邮件的便捷函数"""
    try:
        task = DLKcatTask.objects.get(id=task_id)
        
        if status == 'completed':
            return EmailService.send_calculation_complete_email(task)
        elif status == 'failed':
            return EmailService.send_calculation_failed_email(task)
        elif status == 'processing':
            return EmailService.send_task_started_email(task)
        else:
            logger.warning(f"未知的任务状态: {status}")
            return False
            
    except DLKcatTask.DoesNotExist:
        logger.error(f"任务不存在: {task_id}")
        return False
    except Exception as e:
        logger.error(f"发送通知邮件失败: {e}")
        return False
