import json
import os
import tempfile
import requests
from typing import Dict, List, Any, Optional
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.core.mail import send_mail
from django.conf import settings
from django.urls import reverse
from .models import DLKcatTask, ChatSession
import logging
import uuid

logger = logging.getLogger(__name__)

def validate_compounds_data(compounds_data: List[Dict[str, Any]]) -> bool:
    """验证化合物数据格式"""
    required_fields = ["name", "Kegg_id", "gotenzymes smiles", "Isomeric smiles", "Canonical smiles"]
    
    if not isinstance(compounds_data, list) or len(compounds_data) == 0:
        return False
    
    for compound in compounds_data:
        if not isinstance(compound, dict):
            return False
        for field in required_fields:
            if field not in compound or not compound[field]:
                logger.error(f"化合物数据缺少必需字段: {field}")
                return False
    
    return True

def validate_sequences(sequences: List[str]) -> bool:
    """验证蛋白质序列格式"""
    if not isinstance(sequences, list) or len(sequences) == 0:
        return False
    
    for seq in sequences:
        if not isinstance(seq, str) or len(seq.strip()) == 0:
            return False
        # 简单验证序列只包含氨基酸字符
        valid_chars = set('ACDEFGHIKLMNPQRSTVWY')
        if not all(c.upper() in valid_chars for c in seq.strip()):
            logger.warning(f"序列包含非标准氨基酸字符: {seq[:50]}...")
    
    return True

def create_temp_files(compounds_data: List[Dict[str, Any]], sequences: List[str]) -> tuple:
    """创建临时文件"""
    try:
        # 创建化合物JSON文件
        compounds_content = json.dumps(compounds_data, indent=2, ensure_ascii=False)
        compounds_file = ContentFile(compounds_content.encode('utf-8'), name='compounds.json')
        
        # 创建序列TXT文件
        sequences_content = '\n'.join(sequences)
        sequences_file = ContentFile(sequences_content.encode('utf-8'), name='sequences.txt')
        
        return compounds_file, sequences_file
    
    except Exception as e:
        logger.error(f"创建临时文件失败: {e}")
        raise

def call_calculation_api(compounds_file, sequences_file, email: Optional[str] = None) -> Dict[str, Any]:
    """调用/km/calculation1/接口"""
    try:
        # 准备文件数据
        files = {
            'compounds_file': ('compounds.json', compounds_file.read(), 'application/json'),
            'sequences_file': ('sequences.txt', sequences_file.read(), 'text/plain')
        }
        
        # 重置文件指针
        compounds_file.seek(0)
        sequences_file.seek(0)
        
        data = {}
        if email:
            data['email'] = email
        
        # 构建完整的API URL
        base_url = getattr(settings, 'BASE_URL', 'http://localhost:8000')
        api_url = f"{base_url}/km/calculation1/"
        
        # 发送请求
        response = requests.post(
            api_url,
            files=files,
            data=data,
            timeout=300  # 5分钟超时
        )
        
        if response.status_code == 200:
            try:
                result = response.json()
                return {
                    "success": True,
                    "data": result,
                    "download_url": result.get("download_url"),
                    "message": "计算任务已提交成功"
                }
            except json.JSONDecodeError:
                # 如果返回的不是JSON，可能是直接返回下载链接
                return {
                    "success": True,
                    "download_url": response.text.strip(),
                    "message": "计算完成，结果已准备好下载"
                }
        else:
            logger.error(f"API调用失败: {response.status_code} - {response.text}")
            return {
                "success": False,
                "error": f"计算API调用失败: HTTP {response.status_code}",
                "message": "计算任务提交失败，请稍后重试"
            }
    
    except requests.exceptions.Timeout:
        return {
            "success": False,
            "error": "请求超时",
            "message": "计算任务提交超时，请稍后重试"
        }
    except requests.exceptions.RequestException as e:
        logger.error(f"API请求异常: {e}")
        return {
            "success": False,
            "error": f"网络请求失败: {str(e)}",
            "message": "网络连接失败，请检查网络连接后重试"
        }
    except Exception as e:
        logger.error(f"调用计算API时发生未知错误: {e}")
        return {
            "success": False,
            "error": f"未知错误: {str(e)}",
            "message": "系统内部错误，请联系管理员"
        }

def calculate_dlkcat(compounds_data: List[Dict[str, Any]], 
                    sequences: List[str], 
                    email: Optional[str] = None,
                    session_id: Optional[str] = None) -> Dict[str, Any]:
    """主要的DLKcat计算函数"""
    try:
        # 验证输入数据
        if not validate_compounds_data(compounds_data):
            return {
                "success": False,
                "error": "化合物数据格式不正确",
                "message": "请确保化合物数据包含所有必需字段：name、Kegg_id、gotenzymes smiles、Isomeric smiles、Canonical smiles"
            }
        
        if not validate_sequences(sequences):
            return {
                "success": False,
                "error": "蛋白质序列格式不正确",
                "message": "请确保提供有效的蛋白质序列"
            }
        
        # 创建任务记录
        task = DLKcatTask.objects.create(
            email=email,
            status='pending'
        )
        
        if session_id:
            try:
                session = ChatSession.objects.get(id=session_id)
                task.session = session
                task.save()
            except ChatSession.DoesNotExist:
                pass
        
        # 创建临时文件
        compounds_file, sequences_file = create_temp_files(compounds_data, sequences)
        
        # 保存文件到任务记录
        task.compounds_file.save('compounds.json', compounds_file)
        task.sequences_file.save('sequences.txt', sequences_file)
        task.status = 'processing'
        task.save()
        
        # 调用计算API
        result = call_calculation_api(compounds_file, sequences_file, email)
        
        if result["success"]:
            task.status = 'completed'
            task.download_url = result.get("download_url")
            task.save()
            
            return {
                "success": True,
                "task_id": str(task.id),
                "download_url": result.get("download_url"),
                "message": f"DLKcat计算已完成！任务ID: {task.id}。" + 
                          (f"结果下载链接: {result.get('download_url')}" if result.get('download_url') else "")
            }
        else:
            task.status = 'failed'
            task.error_message = result.get("error", "未知错误")
            task.save()
            
            return {
                "success": False,
                "task_id": str(task.id),
                "error": result.get("error"),
                "message": result.get("message", "计算失败")
            }
    
    except Exception as e:
        logger.error(f"DLKcat计算过程中发生错误: {e}")
        return {
            "success": False,
            "error": f"系统错误: {str(e)}",
            "message": "计算过程中发生系统错误，请联系管理员"
        }

def get_calculation_status(task_id: str) -> Dict[str, Any]:
    """获取计算任务状态"""
    try:
        task = DLKcatTask.objects.get(id=task_id)
        return {
            "success": True,
            "task_id": task_id,
            "status": task.status,
            "created_at": task.created_at.isoformat(),
            "updated_at": task.updated_at.isoformat(),
            "download_url": task.download_url,
            "error_message": task.error_message
        }
    except DLKcatTask.DoesNotExist:
        return {
            "success": False,
            "error": "任务不存在",
            "message": f"找不到ID为 {task_id} 的任务"
        }
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        return {
            "success": False,
            "error": f"系统错误: {str(e)}",
            "message": "获取任务状态时发生错误"
        }

def get_download_url(task_id: str) -> Dict[str, Any]:
    """获取下载链接"""
    try:
        task = DLKcatTask.objects.get(id=task_id)
        if task.status == 'completed' and task.download_url:
            return {
                "success": True,
                "task_id": task_id,
                "download_url": task.download_url,
                "message": "结果文件已准备好下载"
            }
        elif task.status == 'failed':
            return {
                "success": False,
                "error": "任务执行失败",
                "message": task.error_message or "任务执行失败"
            }
        else:
            return {
                "success": False,
                "error": "任务尚未完成",
                "message": f"任务状态: {task.get_status_display()}"
            }
    except DLKcatTask.DoesNotExist:
        return {
            "success": False,
            "error": "任务不存在",
            "message": f"找不到ID为 {task_id} 的任务"
        }
    except Exception as e:
        logger.error(f"获取下载链接失败: {e}")
        return {
            "success": False,
            "error": f"系统错误: {str(e)}",
            "message": "获取下载链接时发生错误"
        }
