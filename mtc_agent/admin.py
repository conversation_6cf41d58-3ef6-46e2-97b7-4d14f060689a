from django.contrib import admin
from .models import ChatSession, ChatMessage, DLKcatTask, UploadedFile

@admin.register(ChatSession)
class ChatSessionAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'user', 'created_at', 'updated_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('title', 'user__username')
    readonly_fields = ('id', 'created_at', 'updated_at')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

@admin.register(ChatMessage)
class ChatMessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'session', 'role', 'content_preview', 'function_name', 'timestamp')
    list_filter = ('role', 'timestamp', 'function_name')
    search_fields = ('content', 'function_name')
    readonly_fields = ('id', 'timestamp')

    def content_preview(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = '内容预览'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('session')

@admin.register(DLKcatTask)
class DLKcatTaskAdmin(admin.ModelAdmin):
    list_display = ('id', 'status', 'user', 'email', 'created_at', 'updated_at')
    list_filter = ('status', 'created_at', 'updated_at')
    search_fields = ('id', 'user__username', 'email')
    readonly_fields = ('id', 'created_at', 'updated_at')

    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'status', 'user', 'session', 'email')
        }),
        ('文件', {
            'fields': ('compounds_file', 'sequences_file', 'result_file')
        }),
        ('结果', {
            'fields': ('download_url', 'error_message')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'session')

@admin.register(UploadedFile)
class UploadedFileAdmin(admin.ModelAdmin):
    list_display = ('id', 'session', 'original_name', 'file_type', 'uploaded_at')
    list_filter = ('file_type', 'uploaded_at')
    search_fields = ('original_name', 'session__id')
    readonly_fields = ('id', 'uploaded_at')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('session')
