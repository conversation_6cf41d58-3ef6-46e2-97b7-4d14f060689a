import json
import requests
from typing import Dict, List, Any, Optional
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class QwenClient:
    """千问大模型API客户端"""
    
    def __init__(self):
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        self.api_key = "sk-b110628e650643c485ae6df6ff433fa1"
        self.model = "qwen-max-latest"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def get_available_functions(self) -> List[Dict[str, Any]]:
        """获取可用的函数定义"""
        return [
            {
                "name": "calculate_dlkcat",
                "description": "计算蛋白质的kcat值，使用DLKcat方法。需要提供化合物JSON文件和蛋白质序列TXT文件。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "compounds_data": {
                            "type": "array",
                            "description": "化合物数据数组，每个化合物包含name、Kegg_id、gotenzymes smiles、Isomeric smiles、Canon<PERSON> smiles字段",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string", "description": "化合物名称"},
                                    "Kegg_id": {"type": "string", "description": "KEGG数据库ID"},
                                    "gotenzymes smiles": {"type": "string", "description": "GoTEnzymes SMILES"},
                                    "Isomeric smiles": {"type": "string", "description": "异构体SMILES"},
                                    "Canonical smiles": {"type": "string", "description": "标准SMILES"}
                                },
                                "required": ["name", "Kegg_id", "gotenzymes smiles", "Isomeric smiles", "Canonical smiles"]
                            }
                        },
                        "sequences": {
                            "type": "array",
                            "description": "蛋白质序列数组，每行一个序列",
                            "items": {"type": "string"}
                        },
                        "email": {
                            "type": "string",
                            "description": "用户邮箱地址（可选），用于发送计算完成通知",
                            "format": "email"
                        }
                    },
                    "required": ["compounds_data", "sequences"]
                }
            },
            {
                "name": "get_calculation_status",
                "description": "获取DLKcat计算任务的状态",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "task_id": {
                            "type": "string",
                            "description": "任务ID"
                        }
                    },
                    "required": ["task_id"]
                }
            },
            {
                "name": "download_result",
                "description": "下载计算结果文件",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "task_id": {
                            "type": "string",
                            "description": "任务ID"
                        }
                    },
                    "required": ["task_id"]
                }
            }
        ]
    
    def chat_completion(self, messages: List[Dict[str, str]], 
                       functions: Optional[List[Dict[str, Any]]] = None,
                       function_call: Optional[str] = None) -> Dict[str, Any]:
        """发送聊天请求到千问API"""
        
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        if functions:
            payload["functions"] = functions
            if function_call:
                payload["function_call"] = function_call
        
        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        
        except requests.exceptions.RequestException as e:
            logger.error(f"千问API请求失败: {e}")
            return {
                "error": f"API请求失败: {str(e)}",
                "choices": [{
                    "message": {
                        "role": "assistant",
                        "content": f"抱歉，我遇到了技术问题：{str(e)}。请稍后再试。"
                    }
                }]
            }
        except Exception as e:
            logger.error(f"千问API处理失败: {e}")
            return {
                "error": f"处理失败: {str(e)}",
                "choices": [{
                    "message": {
                        "role": "assistant", 
                        "content": f"抱歉，处理您的请求时出现错误：{str(e)}"
                    }
                }]
            }
    
    def process_function_call(self, function_name: str, arguments: Dict[str, Any],
                             session_id: Optional[str] = None,
                             processed_files: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """处理函数调用"""
        try:
            if function_name == "calculate_dlkcat":
                from .dlkcat_km import calculate_dlkcat

                # 如果有处理过的文件数据，使用它们
                if processed_files:
                    compounds_data = processed_files.get("compounds_data", [])
                    sequences = processed_files.get("sequences_data", [])
                else:
                    compounds_data = arguments.get("compounds_data", [])
                    sequences = arguments.get("sequences", [])

                return calculate_dlkcat(
                    compounds_data=compounds_data,
                    sequences=sequences,
                    email=arguments.get("email"),
                    session_id=session_id
                )
            elif function_name == "get_calculation_status":
                from .dlkcat_km import get_calculation_status
                return get_calculation_status(arguments.get("task_id"))
            elif function_name == "download_result":
                from .dlkcat_km import get_download_url
                return get_download_url(arguments.get("task_id"))
            else:
                return {"error": f"未知的函数: {function_name}"}

        except Exception as e:
            logger.error(f"函数调用失败 {function_name}: {e}")
            return {"error": f"函数调用失败: {str(e)}"}
