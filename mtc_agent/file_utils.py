import json
import re
from typing import Dict, List, Any, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class FileValidator:
    """文件验证器"""
    
    @staticmethod
    def validate_json_file(content: str) -> Tuple[bool, str, Optional[List[Dict[str, Any]]]]:
        """验证JSON文件格式和内容"""
        try:
            # 解析JSON
            data = json.loads(content)
            
            # 检查是否为数组
            if not isinstance(data, list):
                return False, "JSON文件必须包含一个数组", None
            
            if len(data) == 0:
                return False, "JSON文件不能为空", None
            
            # 验证每个化合物对象
            required_fields = [
                "name", 
                "Kegg_id", 
                "gotenzymes smiles", 
                "Isomeric smiles", 
                "Canonical smiles"
            ]
            
            for i, compound in enumerate(data):
                if not isinstance(compound, dict):
                    return False, f"第{i+1}个元素必须是对象", None
                
                # 检查必需字段
                for field in required_fields:
                    if field not in compound:
                        return False, f"第{i+1}个化合物缺少必需字段: {field}", None
                    
                    if not compound[field] or not isinstance(compound[field], str):
                        return False, f"第{i+1}个化合物的字段 {field} 不能为空且必须是字符串", None
                
                # 验证SMILES格式（简单验证）
                smiles_fields = ["gotenzymes smiles", "Isomeric smiles", "Canonical smiles"]
                for smiles_field in smiles_fields:
                    smiles = compound[smiles_field]
                    if not FileValidator._is_valid_smiles(smiles):
                        logger.warning(f"第{i+1}个化合物的 {smiles_field} 可能不是有效的SMILES: {smiles}")
                
                # 验证Kegg_id格式
                kegg_id = compound["Kegg_id"]
                if not FileValidator._is_valid_kegg_id(kegg_id):
                    logger.warning(f"第{i+1}个化合物的Kegg_id格式可能不正确: {kegg_id}")
            
            return True, f"成功验证 {len(data)} 个化合物", data
            
        except json.JSONDecodeError as e:
            return False, f"JSON格式错误: {str(e)}", None
        except Exception as e:
            return False, f"验证过程中发生错误: {str(e)}", None
    
    @staticmethod
    def validate_txt_file(content: str) -> Tuple[bool, str, Optional[List[str]]]:
        """验证TXT文件格式和内容"""
        try:
            # 分割行并清理
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            
            if len(lines) == 0:
                return False, "TXT文件不能为空", None
            
            sequences = []
            for i, line in enumerate(lines):
                # 检查是否为有效的蛋白质序列
                if not FileValidator._is_valid_protein_sequence(line):
                    return False, f"第{i+1}行不是有效的蛋白质序列: {line[:50]}...", None
                
                sequences.append(line)
            
            return True, f"成功验证 {len(sequences)} 个蛋白质序列", sequences
            
        except Exception as e:
            return False, f"验证过程中发生错误: {str(e)}", None
    
    @staticmethod
    def _is_valid_smiles(smiles: str) -> bool:
        """简单的SMILES格式验证"""
        if not smiles or len(smiles) < 1:
            return False
        
        # 基本的SMILES字符检查
        valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789()[]{}=+-#@/\\.')
        return all(c in valid_chars for c in smiles)
    
    @staticmethod
    def _is_valid_kegg_id(kegg_id: str) -> bool:
        """验证KEGG ID格式"""
        if not kegg_id:
            return False
        
        # KEGG化合物ID通常以C开头，后跟5位数字
        pattern = r'^C\d{5}$'
        return bool(re.match(pattern, kegg_id))
    
    @staticmethod
    def _is_valid_protein_sequence(sequence: str) -> bool:
        """验证蛋白质序列"""
        if not sequence or len(sequence) < 10:  # 最少10个氨基酸
            return False
        
        # 标准氨基酸字符
        valid_amino_acids = set('ACDEFGHIKLMNPQRSTVWY')
        
        # 检查是否只包含有效的氨基酸字符
        sequence_upper = sequence.upper()
        return all(aa in valid_amino_acids for aa in sequence_upper)

class FileProcessor:
    """文件处理器"""
    
    @staticmethod
    def process_uploaded_files(files_data: List[Dict[str, Any]]) -> Tuple[bool, str, Dict[str, Any]]:
        """处理上传的文件数据"""
        try:
            compounds_data = None
            sequences_data = None
            
            for file_data in files_data:
                file_type = file_data.get('type', '')
                content = file_data.get('content', '')
                name = file_data.get('name', '')
                
                if file_type == 'json':
                    # 处理JSON文件
                    is_valid, message, data = FileValidator.validate_json_file(content)
                    if not is_valid:
                        return False, f"化合物文件 {name} 验证失败: {message}", {}
                    compounds_data = data
                    
                elif file_type == 'txt':
                    # 处理TXT文件
                    is_valid, message, data = FileValidator.validate_txt_file(content)
                    if not is_valid:
                        return False, f"序列文件 {name} 验证失败: {message}", {}
                    sequences_data = data
                
                else:
                    return False, f"不支持的文件类型: {file_type}", {}
            
            # 检查是否有必需的文件
            if compounds_data is None:
                return False, "缺少化合物JSON文件", {}
            
            if sequences_data is None:
                return False, "缺少蛋白质序列TXT文件", {}
            
            return True, "文件验证成功", {
                'compounds_data': compounds_data,
                'sequences_data': sequences_data
            }
            
        except Exception as e:
            logger.error(f"文件处理错误: {e}")
            return False, f"文件处理过程中发生错误: {str(e)}", {}
    
    @staticmethod
    def generate_sample_files() -> Dict[str, str]:
        """生成示例文件内容"""
        sample_compounds = [
            {
                "name": "NMN",
                "Kegg_id": "C00153",
                "gotenzymes smiles": "C1=CC(=C[N+](=C1)[C@H]2[C@@H]([C@@H]([C@H](O2)COP(=O)(O)[O-])O)O)C(=O)N",
                "Isomeric smiles": "C1=CC(=C[N+](=C1)[C@H]2[C@@H]([C@@H]([C@H](O2)COP(=O)(O)[O-])O)O)C(=O)N",
                "Canonical smiles": "C1=CC(=C[N+](=C1)[C@H]2[C@@H]([C@@H]([C@H](O2)COP(=O)(O)[O-])O)O)C(=O)N"
            },
            {
                "name": "Diphosphate",
                "Kegg_id": "C00154",
                "gotenzymes smiles": "OP(=O)(O)OP(=O)(O)O",
                "Isomeric smiles": "OP(=O)(O)OP(=O)(O)O",
                "Canonical smiles": "OP(=O)(O)OP(=O)(O)O"
            }
        ]
        
        sample_sequences = [
            "MESAAVGAEFNILLATDSYKVTHYKQYPPNTSKVYSYFECREKKTENSKFRKVKYEETVFYGLQYILHKYLKGRVVTKEKIQEAKEVYKEHFQDDVFNEPGWNYILEKYEGCLPIEVKAVPEGSVVPRGNVLFTVENTDPDCYWLTNWIETILVQSWYPITVATNSREQKKILAKYLLETSGSLEGLEYKLHDFGYRGVSSQETAGIGASAHLVNFKGTDTVAGIGLIKKYYGTKDPVPGYSIPAAEHSTITAWGKDHEKDAFEHIVTQFSSVPVSVVSDSYDIYNACEKIWGEDLRHLIESRSAEAPLIIRPDSGNPLDTVLKVLEILERKFPITENEKGYKVLPPYLRIIQGDGVDINTLQEMVEGMKQNKWSIENISFGSGGALLQKITRDLLNCSFKCSYVVTNGLGVNVFKDPVADPNKRSKKGRLSLHRTPDGDFVTLEEGKGDLEEYGQDLLHTVFRNGIVTKSYSFDDVRKNAQLEISELTVASH",
            "MHDSAAAEISSLQYYAAIAIFLITYAIIISEKINRAVIAMLGAALMIMFGIVDLHNAFTQHIEWGTITLLIGMMILVGITSKSGFFQYVAIKAAKLAKGRPLRILVMLSLLTGALSAFLDNVTTVLLIVPVTFSITRMLQVNPVPYLISEVLFSNIGGTATLIGDPPNIMIGSANKHLDFNAFLFNLTPIVLIIMIVTVSILVFIYRRQLKTDDHLVNKLMNVNEAEYIKDAALLKKSVSVLFLTILGFLLHSVIHVDAAVIAMTGAIILMLIGVQEHEIEDVFASVEWVTIFFFAGLFTLVGGLVDIGFIKSLAEKVLEVTGGDISAAAYFILWVSGLASATIDNIPFVATMIPLIKDMAAGMGLSPDSAQMEVLWWALSLGACLGGNGTLIGASANVIVAGIASREGHGFSYMDFLKIGAPLTFIALLLSHIYLFVRYLM"
        ]
        
        return {
            'compounds.json': json.dumps(sample_compounds, indent=2, ensure_ascii=False),
            'sequences.txt': '\n'.join(sample_sequences)
        }
    
    @staticmethod
    def get_file_info(content: str, file_type: str) -> Dict[str, Any]:
        """获取文件信息"""
        info = {
            'size': len(content.encode('utf-8')),
            'lines': len(content.split('\n')),
            'type': file_type
        }
        
        if file_type == 'json':
            try:
                data = json.loads(content)
                if isinstance(data, list):
                    info['compounds_count'] = len(data)
                    info['sample_compound'] = data[0] if data else None
            except:
                pass
        
        elif file_type == 'txt':
            sequences = [line.strip() for line in content.split('\n') if line.strip()]
            info['sequences_count'] = len(sequences)
            info['avg_sequence_length'] = sum(len(seq) for seq in sequences) / len(sequences) if sequences else 0
        
        return info
